# Changelog

All notable changes to this project will be documented in this file.

## [2.9.0] - 2025-07-29

### Features

- Migrate user ids (#223)
- Adding voucher code to gross revenue table statistics (#225)

### Miscellaneous Tasks

- Update reviewer lottery (#222)
- Delete user migrator (#224)

## [2.8.3] - 2025-07-07

### Bug Fixes

- Add order_by to count_sold_tickets reports (#220)

### Miscellaneous Tasks

- *(release)* V2.8.3 (#221)

## [2.8.2] - 2025-06-25

### Refactor

- Remove expensive outer join (#218)

### Miscellaneous Tasks

- *(release)* V2.8.2 (#219)

## [2.8.1] - 2025-06-17

### Bug Fixes

- Change distribution type filter to ticket.distribution_type from variant.distribution_type (#215)
- Extend entrance-areas API doc by distribution type (#216)

### Miscellaneous Tasks

- *(release)* V2.8.1 (#217)

## [2.8.0] - 2025-06-05

### Features

- Adding distribution_type filter to entrance-area reports (#211)

### Miscellaneous Tasks

- *(release)* V2.8.0 (#214)

## [2.7.0] - 2025-05-27

### Features

- *(DB)* Add db dump to set up a new database more easily (#208)
- 4800 sales statistics (#209)

### Bug Fixes

- Adding distribution type nil to count_sold_tickets (#210)
- Add swapped tickets to sales report stats (#212)

### Miscellaneous Tasks

- *(release)* V2.7.0

## [2.6.0] - 2025-05-22

### Features

- Adding gzip compression to configs (#206)

### Miscellaneous Tasks

- *(release)* V2.6.0 (#207)

## [2.5.0] - 2025-05-16

### Features

- Add distribution type filter to events dashboard (#204)

### Miscellaneous Tasks

- *(release)* V2.5.0 (#205)

## [2.4.0] - 2025-05-15

### Features

- Expand events dashboard API (#200)

### Refactor

- Read IP from an ENV (#201)

### Miscellaneous Tasks

- Clean up code (#202)
- *(release)* V2.4.0 (#203)

## [2.3.0] - 2025-05-08

### Features

- Remove CloudRun deployment for dev environment (#196)
- Use scope_id and scope for vouchers (#198)

### Miscellaneous Tasks

- *(release)* V2.3.0 (#199)

## [2.2.0] - 2025-04-03

### Features

- Adding ticket category id and name to variant sales_statistics api (#193)

### Bug Fixes

- Runtime config minikube ip (#191)

### Miscellaneous Tasks

- *(release)* V2.2.0 (#194)

## [2.1.0] - 2025-04-02

### Features

- Displaying price level in sales export (#182)
- Adding mlpm and admission to variant sales_statistics api (#188)

### Bug Fixes

- Adding missing fees column to ticket sales export in csv and pdf (#179)
- Properly calculate fees with discount (#180)
- Reorder ticket sales report columns (#189)

### Miscellaneous Tasks

- Update mirrord script to use minikube context (#183)
- *(release)* V2.1.0 (#190)

## [2.0.0] - 2025-03-13

### Features

- *(router)* [**breaking**] Remove deprecated APIs (#177)

### Miscellaneous Tasks

- *(release)* V2.0.0 (#178)

## [1.11.0] - 2025-03-11

### Features

- Use BACKEND_URL ENV to configure service client
- Use BACKEND_URL ENV to configure service client (#171)

### Bug Fixes

- Read ENV instead of Secret

### Miscellaneous Tasks

- Clean up dev.exs
- Add BACKEND_URL to .env.template
- *(release)* V1.11.0
- *(release)* V1.11.0 (#176)

## [1.10.0] - 2025-03-11

### Features

- Release management (#168)
- Deactivate deprecated APIs (#169)
- Use Artifact Registry instead of GCR

### Bug Fixes

- Don't mark new API as deprecated (#170)
- Use correct naming

### Miscellaneous Tasks

- Format cloudbuild file
- *(release)* V1.10.0

## [1.9.3] - 2025-02-25

### Miscellaneous Tasks

- Adding changeset_json.ex for standardized param validation error responses (#162)
- Udpate service_client to use retries (#166)
- Update patch version (#167)

## [1.9.2] - 2025-02-12

### Bug Fixes

- Adding vc.sold to entrance query for sort by ratio (#159)

### Miscellaneous Tasks

- Bump version to 1.9.2 (#160)

## [1.9.1] - 2025-02-10

### Miscellaneous Tasks

- Update patch version to v1.9.1 (#158)

## [1.9.0] - 2025-01-30

### Features

- Use variable for reviewer lottery config (#149)
- User personal_information (location) instead of  billing address (#153)

### Bug Fixes

- Trapping sigterm and killing beam process on shutdown (#148)
- Use  correct syntax for global variables in GitHub action

### Other

- Add GitHub action output

### Miscellaneous Tasks

- Use GitHub variable next try
- Use GitHub variable next try
- Use GitHub variable next try
- Use GitHub variable next try
- Use GitHub variable next try and clean up yml
- Update minor version to v1.9.0 (#155)

## [1.8.5] - 2025-01-02

### Miscellaneous Tasks

- Update patch version to v1.8.5 (#145)

## [1.8.4] - 2024-12-23

### Bug Fixes

- Add empty csv error handling (#142)

### Miscellaneous Tasks

- Update patch version (#143)

## [1.8.3] - 2024-12-20

### Miscellaneous Tasks

- Update patch version (#141)

## [1.8.2] - 2024-12-19

### Bug Fixes

- Add guestlist tickets to reportable tickets (#138)

### Miscellaneous Tasks

- Update patch version (#139)

## [1.8.1] - 2024-12-05

### Miscellaneous Tasks

- Update ex_service_client (#132)
- Update patch version to v1.8.1 (#135)

## [1.8.0] - 2024-12-03

### Features

- Add migration and server exit status to docker-start.sh (#128)
- [**breaking**] Remove sales periods from service (#129)

### Miscellaneous Tasks

- Update minor version to v1.8.0 (#131)

## [1.7.1] - 2024-11-26

### Features

- Remove sales_periods from dashboard reports (#126)

### Miscellaneous Tasks

- Update patch version (#127)

## [1.7.0] - 2024-11-26

### Features

- Ticket sales export, switch category name and product type o… (#121)

### Bug Fixes

- Remove rm -rf /root/.ssh from cloudbuild.yaml (#122)
- Add charset=UTF-8 to pdf export html (#123)

### Miscellaneous Tasks

- Update minor version (#125)

## [1.6.5] - 2024-11-19

### Features

- Make SalesPeriod optional (#116)
- Remove draft events from ticket sales overview (#118)
- Remove draft events from total sales dashboard (#119)

### Miscellaneous Tasks

- Update patch version to v1.6.4 (#120)

## [1.6.4] - 2024-11-16

### Bug Fixes

- Remove typo

### Miscellaneous Tasks

- Update patch version to v1.6.3

## [1.6.3] - 2024-11-16

### Features

- Change default sorting for entrance areas reports (product overview)
- *(workaround)* Use all entrance areas if no entrance area is send by client

### Miscellaneous Tasks

- Update patch version to v1.6.2

## [1.6.1] - 2024-11-15

### Features

- *(workaround)* Add sales period name to product / name (#114)
- Add total Attendees to entrance areas reports (#113)

### Miscellaneous Tasks

- Update patch verstion to v1.6.1 (#115)

## [1.6.0] - 2024-11-11

### Features

- Remove product type from product overview statistics (#110)

### Bug Fixes

- Empty error (#103)
- Date comparison (#105)
- Date conversion to utc (#106)

### Miscellaneous Tasks

- Change end date calculation (#107)
- Filter duplicate data in series (#108)
- Filter product overview table base on entrance areas (#109)
- Bump to version 1.6.0 (#111)

## [1.5.3] - 2024-11-06

### Features

- Time-lapse stats (#98)
- Add Jan to Reviewer Lottery (#101)

### Bug Fixes

- Page number correctly set and fill blank time lapse stats (#102)

### Other

- Use variant name as category name (#104)

### Miscellaneous Tasks

- Update patch version to v1.5.3

## [1.5.2] - 2024-11-04

### Bug Fixes

- Server side sorting for entrance area list stats (#97)

### Miscellaneous Tasks

- Rename entrance_area_id to location_id (#94)
- Round intervals for entry flow stats (#95)
- Update version
- Update ex_service_client (#96)
- Update patch version to v1.5.2

## [1.5.0] - 2024-10-29

### Features

- Add sorting to product-overview (#87)
- Fix typo on order by function name (#88)

### Bug Fixes

- Configure CORS Plug, allow  x-api-experimental header (#77)
- Make reports-service mirrorable for minikube (#92)

### Miscellaneous Tasks

- Rename entrance_area_ticket_areas to entrance_area_ticket_categories (#84)
- Add missing params to api spec (#86)
- Product overview avg calculation and sorting (#91)
- Update minor version to v1.5.0 (#93)

## [1.4.4] - 2024-09-26

### Features

- Add admission to events dashbaord APIs (#76)

### Miscellaneous Tasks

- Update patch version to v1.4.4

## [1.4.3] - 2024-09-25

### Features

- *(reports,svc,gke)* Add annotated gke deployment [SI-205][SI-220]

### Bug Fixes

- Update tzdata, use db_seeds (#75)

### Miscellaneous Tasks

- Update patch version to v1.4.3

## [1.4.2] - 2024-09-19

### Miscellaneous Tasks

- Update ex_ikarus (#70)
- Update patch version to v1.4.2

## [1.4.1] - 2024-09-17

### Bug Fixes

- Remove deleted variants from dashboard

### Miscellaneous Tasks

- Update patch version to v1.4.1

## [1.4.0] - 2024-09-16

### Features

- Update Ikarus to 1.0.0 and add an experimental health check (#67)

### Miscellaneous Tasks

- Update minor version to v1.4.0

## [1.3.4] - 2024-09-06

### Features

- Add db connection heartbeat (#64)

### Miscellaneous Tasks

- Update patch version to v1.3.3
- Update patch version to v1.3.4

## [1.3.3] - 2024-09-02

### Features

- Add ExIkarus (#59)
- Add created on date to ticket sales pdf (#60)
- Add timezone to ticket sales export pdf | pin versions (#61)

### Bug Fixes

- Pin yamlr version (#62)

### Miscellaneous Tasks

- Update mix.lock
- Update README.md to include wkhtmltopdf installation (#57)
- Bump version to 1.3.2

## [1.3.1] - 2024-08-08

### Features

- Remove all order and bill reference from ticket schema and queries (#51)

### Miscellaneous Tasks

- Bump to version 1.3.1

## [1.3.0] - 2024-08-01

### Features

- Add dependabot (#47)
- Change amout for tickets api, add null values

### Miscellaneous Tasks

- Upate minor version to v1.3.0

## [1.2.6] - 2024-07-30

### Bug Fixes

- Allow event admins to use sales comparison (#46)

### Miscellaneous Tasks

- Update patch version to v1.2.6

## [1.2.5] - 2024-07-29

### Features

- Add reviewer-lottery (#41)

### Bug Fixes

- Add forgotten s
- Use better errror handling to prevent 500er response (#45)

### Miscellaneous Tasks

- Remove CODEOWNERS (#42)
- Move github action to workflows
- Update patch version to v1.2.5

## [1.2.4] - 2024-07-04

### Bug Fixes

- Remove null from gender reports (#40)

### Miscellaneous Tasks

- Update patch version to v1.2.4

## [1.2.3] - 2024-07-03

### Bug Fixes

- Add pattern match index function for backwarts compability

### Miscellaneous Tasks

- Update patch version to v1.2.3

## [1.2.2] - 2024-07-03

### Bug Fixes

- Get paid orders report (#39)

### Miscellaneous Tasks

- Update patch version to v1.2.2

## [1.2.1] - 2024-07-03

### Miscellaneous Tasks

- Update patch version to v1.2.1

## [1.2.0] - 2024-06-28

### Miscellaneous Tasks

- Update minor version to v1.2.0

## [1.1.20] - 2024-06-27

### Bug Fixes

- Correct OpenApi behaviour line

### Miscellaneous Tasks

- Bump elixir-styler to 1.0.0-rc.2 (#35)
- Update patch version to v1.1.20

## [1.1.19] - 2024-06-19

### Bug Fixes

- Add admin check to orders apis
- Add admin call events demographics apis
- Add admin check to ticket api

### Refactor

- Move permission check for admin user in a seperate function

### Miscellaneous Tasks

- Remove unused aliases
- Update patch version to v1.1.19

## [1.1.18] - 2024-06-19

### Bug Fixes

- Add one more date format for csv import

### Miscellaneous Tasks

- Update patch version to v1.1.18

## [1.1.17] - 2024-06-19

### Miscellaneous Tasks

- Update Logger
- Update patch version to v1.1.17

## [1.1.14] - 2024-06-17

### Miscellaneous Tasks

- Bump version to 1.1.14

## [1.1.13] - 2024-06-17

### Features

- Return all combination of gender and age group

### Miscellaneous Tasks

- PR change request
- Bump patch version to v1.1.13

## [1.1.12] - 2024-06-12

### Bug Fixes

- Revert formatting bug

## [1.1.11] - 2024-06-12

### Bug Fixes

- Update sql query to get organizer for a logged in user

### Miscellaneous Tasks

- Update mix.lock

## [1.1.10] - 2024-06-11

### Bug Fixes

- Add organizer to general ticket query

### Miscellaneous Tasks

- Bump patch version to v1.1.10

## [1.1.9] - 2024-06-11

### Bug Fixes

- Count distinct tickets
- Add where condititon to admin query

### Miscellaneous Tasks

- Bump patch version to v1.1.9

## [1.1.8] - 2024-06-04

### Bug Fixes

- Remove fix sales period for ticket sales

### Miscellaneous Tasks

- Bump patch version to v1.1.8

## [1.1.7] - 2024-06-04

### Bug Fixes

- Repair migration

### Miscellaneous Tasks

- Bump patch version to v1.1.7

## [1.1.6] - 2024-06-04

### Features

- Add imported_events and imported_orders
- Add organizer API to list all events
- Add tickets api for Stagedates events
- Fill ticket response with empty values
- Add tickets APIs for imported events
- Add file upload api
- Add ticketio parsing
- Write imported event into postgres
- Change date format for ticketio and use semicolon as seperator for the CSVs
- Add a better error handling, resolve some change requests

### Bug Fixes

- Remove typo
- Use right type for money attributes
- Add cldr and fix configuration
- Use ipv4 for local environment
- Rename function
- Add missing routes
- Use right permissions
- Use organizer instead of promoter in events
- Remove pattern match
- Remove typo
- Make integer parsing more stable
- Write correct order into database
- Change transaction timeout

### Other

- Add Logging infos for uploaded file
- Change pattern matching

### Refactor

- *(organizer)* Rename promoter to organizer
- Rename function
- Clean up code, add documentaion

### Documentation

- Update API documentation

### Styling

- Mix format

### Miscellaneous Tasks

- Remove Dennis from CODEOWNERS
- Create .coderabbit.yaml and set assertive mode
- Update mix.lock
- Resolve pr change requests
- Run mix format
- Add don't written changes
- Bump patch version to v1.1.6

## [1.1.5] - 2024-05-02

### Miscellaneous Tasks

- Set ex_firebase_auth_plug environment to local in dev config
- Bump patch version to v1.1.5

## [1.1.4] - 2024-04-26

### Miscellaneous Tasks

- Bump service_client to 0.1.48 | fetch environment from env | bump version to 1.1.4

## [1.1.3] - 2024-04-23

### Bug Fixes

- Fetching ex_firebase_auth_plug environment param from env

### Miscellaneous Tasks

- Remove minikube config
- Bump ex_firebase_auth version to 0.2.13
- Bump version to 1.1.3

## [1.1.2] - 2024-04-15

### Features

- Hide internal_sales_period

### Styling

- Change pattern match order

### Miscellaneous Tasks

- Clean up variant name
- Update patch version

## [1.1.1] - 2024-04-11

### Features

- Add admission filter to ticket APIs
- Split counter in tickets and extras
- Change variant name
- Add Noutcha to CODEOWNERS
- Add sold_products to API responses
- Add extras to each response

### Bug Fixes

- Remove copy and past error

### Styling

- Rename private function

### Miscellaneous Tasks

- Add all codeowners
- Resolve change requests
- Move swaggerui to /events/api | generate api doc
- Add elixir-styler
- Run mix format
- Update patch version to v1.1.1

## [1.1.0] - 2024-03-20

### Features

- Add ticket_status and ticket_history

### Bug Fixes

- Add is_deleted flag again

### Miscellaneous Tasks

- Update minor version to v1.1.0

## [1.0.8] - 2024-03-07

### Bug Fixes

- Add cache control header

## [1.0.7] - 2024-03-05

### Other

- Excluding invitation_order entries from report queries

### Miscellaneous Tasks

- Bump version to 1.0.6
- Bump version to 1.0.7

## [1.0.6] - 2024-01-30

### Bug Fixes

- Make all dashboards usable for admin users

## [1.0.4] - 2024-01-25

### Bug Fixes

- Add total_sales to admin dashboards

## [1.0.3] - 2024-01-25

### Bug Fixes

- Create dashboards for admin users

## [1.0.2] - 2024-01-25

### Bug Fixes

- Use the name nameing for relative data

### Miscellaneous Tasks

- Remove IO.inspect

## [1.0.1] - 2024-01-25

### Bug Fixes

- Filter paid orders for ticket counter

## [1.0.0] - 2024-01-25

### Features

- Add promoter dashboard API
- Update user verification
- Use all reportable events for a user
- Add events dashboard apis
- Add ticket dashboards apis
- Add avg to tickets dashboards
- Add ticket details to ticket dashboard
- Add ticket dashboard for day of week - hour matrix

### Bug Fixes

- Add missing permission check
- Use 100% quota for relative dashboards
- Add permissions check
- Use category name instead of id

### Documentation

- Add some OpenAPI documentation

### Styling

- Clean up code and make credo happy

### Miscellaneous Tasks

- Add CORS plugin
- Rename some secrets

## [0.1.0] - 2024-01-24

### Features

- Create reports-service
- Add cloudbuild.yaml
- Add health API
- Create run-local
- Add OpenAPI and SwaggerUI
- Add minikube config
- Add health endpoint to / path

### Bug Fixes

- Use secret instead of env
- Add missing secrets
- Remove unused secret
- Add hex authentication
- Define path for hex_key file
- *(build)* Add hex_key to container
- Remove unused child
- Use correct secret for phoenix port
- Remove typo
- Do not use orders_service config here
- Use IP db connection and fix dockerfile
- Use right pathes and use speaking names
- Add missing lines

### Refactor

- Clean up code and resolve change requests

### Miscellaneous Tasks

- Disable cr re-create [SD1-2052][SI-7]
- Move health endpoint
- Remove dockerignore
- Kubectl update deployment image [SD1-2052][SI-7]
- Fix file permissions [SD1-2052][SI-7][SD1-2048]

<!-- generated by git-cliff -->
