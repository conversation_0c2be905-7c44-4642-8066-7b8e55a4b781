# Changelog

All notable changes to this project will be documented in this file.

## [0.2.0] - 2025-07-25

### Features

- *(image,artifact,registry)* Update artifact registry repo path
- *(image,artifact,registry)* Update artifact registry repo path (#6)
- Implement short link redirection with handler
- *(local,startscript)* Improve go module refresh fnc
- *(database)* Add user permission extension to database file
- *(database)* Add user permission extension to database file
- *(database)* Compress database file
- Add temporary docker compose
- Add logging functions
- Refactor and improve logging and signal processing
- Add db dump for local development
- Add docker compose file
- Add k8s health check [SI-311][SI-268]
- Add k8s health check [SI-311][SI-268]
- *(gofr)* Enable gofr [SI-311][SI-268]
- *(svc,shortlink,platform)* Add shortlink's build instructions
- *(svc,shortlink)* Add build definition [SI-268]

### Bug Fixes

- Adjust test script permissions
- Add os exit to graceful shutdown
- Align go versions (#9)
- Fix healthcheck
- *(shortlink,svc,runtime,redis)* Remove redis usage (#5)
- *(shortlink,svc,runtime,chi router)* Fix go stack
- *(runtime)* Extend service environment Ref: SD1-4944 SD1-4357
- Correct error message for short link retrieval

### Refactor

- Improve redirection logic
- Apply Go naming conventions to function names

### Documentation

- Update README with prerequisites and local run instructions

### Testing

- Add script for asserting redirection link

### Miscellaneous Tasks

- *(release)* V0.1.0
- *(release)* V0.1.0
- Cleanup app start (#10)
- Cleanup
- Ignore local environment configuration file
- Bump goFr to v1.39.1
- Add "/configs/.local.env" to .gitignore
- Fix docker compose file
- Force remove docker compose file
- Force remove docker compose file
- Solve merge conflict
- Force remove docker compose file
- Resolve merge conflict

## [0.0.1] - 2024-10-31

<!-- generated by git-cliff -->
