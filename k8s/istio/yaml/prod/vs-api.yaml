# ╔════════════════════════════════════════════════════════════════════════════════╗
# ║ Version:                                                                    03 ║
# ║ Date of Version:                                                    03.06.2025 ║
# ║ Owner:                                                                      SD ║
# ║ Classification:                                                       Internal ║
# ║ Distribution:                                                        All Staff ║
# ╚════════════════════════════════════════════════════════════════════════════════╝
---
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: sd-api
  namespace: stdts-prod
spec:
  hosts:
  - "api.stagedates.com"
  gateways:
  - istio-system/sd-api
  http:

  - name: "api-main-rewrite"

    match:
    - uri:
        prefix: /.well-known/acme-challenge/ # This is needed for cert-manager's HTTP-01 challenge
    route:
    - destination:
        host: future-demand-gke.stdts-prod.svc.cluster.local
        port:
          number: 8080

# external dependency - future demand callback
  - name: "api-rewrite-future-demand-ext"
    match:
    - uri:
        prefix: "/api/future-demand"
    rewrite:
      uri: "/future-demand/api"
    route:
    - destination:
        host: future-demand-gke.stdts-prod.svc.cluster.local
        port:
          number: 8080

  - name: "api-rewrite-future-demand"
    match:
    - uri:
        prefix: "/future-demand/api"
    rewrite:
      uri: "/future-demand/api"
    route:
    - destination:
        host: future-demand-gke.stdts-prod.svc.cluster.local
        port:
          number: 8080
  
  - name: "api-rewrite-carts"
    match:
    - uri:
        prefix: "/carts/api"
    rewrite:
      uri: "/"
    route:
    - destination:
        host: carts-gke.stdts-prod.svc.cluster.local
        port:
          number: 8080

  - name: "api-rewrite-coms"
    match:
    - uri:
        prefix: "/coms/api"
    rewrite:
      uri: "/coms/api"
    route:
    - destination:
        host: com-gke.stdts-prod.svc.cluster.local
        port:
          number: 8080

  - name: "api-rewrite-reports"
    match:
    - uri:
        prefix: "/reports/api"
    rewrite:
      uri: "/reports/api"
    route:
    - destination:
        host: reports-gke.stdts-prod.svc.cluster.local
        port:
          number: 8080

  - name: "api-rewrite-accounts"
    match:
    - uri:
        prefix: "/accounts/api"
    rewrite:
      uri: "/accounts/api"
    route:
    - destination:
        host: accounts-gke.stdts-prod.svc.cluster.local
        port:
          number: 8080

  - name: "api-rewrite-orders"
    match:
    - uri:
        prefix: "/orders/api"
    rewrite:
      uri: "/orders/api"
    route:
    - destination:
        host: orders-gke.stdts-prod.svc.cluster.local
        port:
          number: 8080

  - name: "api-rewrite-emails"
    match:
    - uri:
        prefix: "/emails/api"
    rewrite:
      uri: "/emails/api"
    route:
    - destination:
        host: emails-gke.stdts-prod.svc.cluster.local
        port:
          number: 8080

  - name: "api-rewrite-events"
    match:
    - uri:
        prefix: "/events/api"
    rewrite:
      uri: "/events/api"
    route:
    - destination:
        host: events-gke.stdts-prod.svc.cluster.local
        port:
          number: 8080


  - name: "api-rewrite-events-sockets"
    match:
    - uri:
        prefix: "/events/socket"
    rewrite:
      uri: "/events/socket"
    route:
    - destination:
        host: events-gke.stdts-prod.svc.cluster.local
        port:
          number: 8080

  - name: "api-rewrite-profanity"
    match:
    - uri:
        prefix: "/profanity/api"
    rewrite:
      uri: "/profanity/api"
    route:
    - destination:
        host: profanity-gke.stdts-prod.svc.cluster.local
        port:
          number: 8080

##    match:
##    - uri:
##        prefix: "/waiting-rooms/api"
##    rewrite:
##      uri: "/waiting-rooms/api"
##    route:
##    - destination:
##        host: waiting-rooms-gke.stdts-prod.svc.cluster.local
##        port:
##          number: 8080

  - name: "api-rewrite-pdf"
    match:
    - uri:
          prefix: "/pdf/api"
    rewrite:
      uri: "/pdf/api"
    route:
    - destination:
        host: pdf-gke.stdts-prod.svc.cluster.local
        port:
          number: 8080

  - name: "api-redirect-to-play"
    match:
    - uri:
        exact: "/goto/admission-android"
    redirect:
      redirectCode: 307
      scheme: https
      authority: play.google.com
      uri: "/store/apps/details?id=com.stagedates.admission"

  - name: "api-redirect-to-apple"
    match:
    - uri:
        exact: "/goto/admission-ios"
    redirect:
      redirectCode: 307
      scheme: https
      authority: apps.apple.com
      uri: "/de/app/stagedates/6466404674"

  - name: "api-redirect-to-sitemap-bucket"
    match:
    - uri:
        exact: "/seo/sitemap.xml"
    redirect:
      redirectCode: 307
      scheme: https
      authority: storage.googleapis.com
      uri: "/stdts-prod-cdn-bucket/seo/sitemap.xml"

  - name: "api-redirect-to-dev-apple-developer-merchantid"
    match:
    - uri:
        exact: "/.well-known/dev-apple-developer-merchantid-domain-association.txt"
    redirect:
      redirectCode: 307
      scheme: https
      authority: stagedates.com
      uri: "/dev-apple-developer-merchantid-domain-association.txt"

  - name: "api-redirect-to-prod-apple-developer-merchantid"
    match:
    - uri:
        exact: "/.well-known/prod-apple-developer-merchantid-domain-association.txt"
    redirect:
      redirectCode: 307
      scheme: https
      authority: stagedates.com
      uri: "/prod-apple-developer-merchantid-domain-association.txt"

