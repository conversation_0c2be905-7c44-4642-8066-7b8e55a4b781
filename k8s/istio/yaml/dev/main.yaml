# ╔════════════════════════════════════════════════════════════════════════════════╗
# ║ Version:                                                                    04 ║
# ║ Date of Version:                                                    29.01.2025 ║
# ║ Owner:                                                                      SD ║
# ║ Classification:                                                       Internal ║
# ║ Distribution:                                                        All Staff ║
# ╚════════════════════════════════════════════════════════════════════════════════╝

# This File contains the main service mesh and ingress traffic settings for the main project namespace

## ╔════════════════════════════════════════════════════════════════════════════════╗
## ║ Ingress Gateway                                                                ║
## ╚════════════════════════════════════════════════════════════════════════════════╝
---
apiVersion: networking.istio.io/v1alpha3
kind: Gateway
metadata:
  name: sd-api-dev-es
  namespace: istio-system
spec:
  selector:
    istio: ingressgateway # use Istio default gateway implementation
  servers:
  - port:
      number: 80
      name: http
      protocol: HTTP
    hosts:
    - "api.dev.stagedat.es"
  - port:
      number: 443
      name: https
      protocol: HTTPS
    tls:
      mode: SIMPLE
      credentialName: api-dev-stagedat-es-tls # This should match the Certificate secretName
    hosts:
    - "api.dev.stagedat.es"

## ╔════════════════════════════════════════════════════════════════════════════════╗
## ║ Virtual Services                                                               ║
## ╚════════════════════════════════════════════════════════════════════════════════╝

---
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: sd-api-dev-es
  namespace: stdts-dev
  labels:
    istio.io/rev: 1-23-3
spec:
  hosts:
  - "api.dev.stagedat.es"
  gateways:
  - istio-system/sd-api-dev-es
  http:

  - name: "api-main-rewrite-acme-challenge"
    match:
    - uri:
        prefix: /.well-known/acme-challenge/ # This is needed for cert-manager's HTTP-01 challenge
    route:
    - destination:
        host: future-demand-gke.stdts-dev.svc.cluster.local
        port:
          number: 8080

# external dependency - future demand callback

  - name: "api-rewrite-future-demand-ext"
    match:
    - uri:
        prefix: "/api/future-demand"
    rewrite:
      uri: "/future-demand/api"
    route:
    - destination:
        host: future-demand-gke.stdts-dev.svc.cluster.local
        port:
          number: 8080


  - name: "api-rewrite-future-demand"
    match:
    - uri:
        prefix: "/future-demand/api"
    rewrite:
      uri: "/future-demand/api"
    route:
    - destination:
        host: future-demand-gke.stdts-dev.svc.cluster.local
        port:
          number: 8080

 
  - name: "api-rewrite-carts" 
    match:
    - uri:
        prefix: "/carts/api"
    rewrite:
      uri: "/"
    route:
    - destination:
        host: carts-gke.stdts-dev.svc.cluster.local
        port:
          number: 8080


  - name: "api-rewrite-coms"
    match:
    - uri:
        prefix: "/coms/api"
    rewrite:
      uri: "/coms/api"
    route:
    - destination:
        host: com-gke.stdts-dev.svc.cluster.local
        port:
          number: 8080


  - name: "api-rewrite-reports"
    match:
    - uri:
        prefix: "/reports/api"
    rewrite:
      uri: "/reports/api"
    route:
    - destination:
        host: reports-gke.stdts-dev.svc.cluster.local
        port:
          number: 8080

    # header based traffic routing
    # testing specific headers
    # match:
    # - headers:
    #     x-sd-header:
    #       exact: "sd-monitoring"
    #   uri:
    #     prefix: "/reports/api"
    #   ignoreUriCase: true
    # rewrite:
    #   uri: "/reports/api"
    # route:
    # - destination:
    #     host: reports-gke.stdts-dev.svc.cluster.local
    #     port:
    #       number: 8080


  - name: "api-rewrite-accounts"
    match:
    - uri:
        prefix: "/accounts/api"
    rewrite:
      uri: "/accounts/api"
    route:
    - destination:
        host: accounts-gke.stdts-dev.svc.cluster.local
        port:
          number: 8080


  - name: "api-rewrite-orders"
    match:
    - uri:
        prefix: "/orders/api"
    rewrite:
      uri: "/orders/api"
    route:
    - destination:
        host: orders-gke.stdts-dev.svc.cluster.local
        port:
          number: 8080


  - name: "api-rewrite-emails"
    match:
    - uri:
        prefix: "/emails/api"
    rewrite:
      uri: "/emails/api"
    route:
    - destination:
        host: emails-gke.stdts-dev.svc.cluster.local
        port:
          number: 8080


  - name: "api-rewrite-events"
    match:
    - uri:
        prefix: "/events/api"
    rewrite:
      uri: "/events/api"
    route:
    - destination:
        host: events-gke.stdts-dev.svc.cluster.local
        port:
          number: 8080

  - name: "api-rewrite-events-sockets"
    match:
    - uri:
        prefix: "/events/socket"
    rewrite:
      uri: "/events/socket"
    route:
    - destination:
        host: events-gke.stdts-dev.svc.cluster.local
        port:
          number: 8080

  - name: "api-rewrite-profanity"
    match:
    - uri:
        prefix: "/profanity/api"
    rewrite:
      uri: "/profanity/api"
    route:
    - destination:
        host: profanity-gke.stdts-dev.svc.cluster.local
        port:
          number: 8080

  - name: "api-rewrite-waiting-room"
    match:
    - uri:
        prefix: "/waiting-rooms/api"
    rewrite:
      uri: "/waiting-rooms/api"
    route:
    - destination:
        host: waiting-rooms-gke.stdts-dev.svc.cluster.local
        port:
          number: 8080

  - name: "api-rewrite-waiting-pdf"
    match:
    - uri:
        prefix: "/pdf/api"
    rewrite:
      uri: "/pdf/api"
    route:
    - destination:
        host: pdf-gke.stdts-dev.svc.cluster.local
        port:
          number: 8080

## ╔════════════════════════════════════════════════════════════════════════════════╗
## ║ Redirects                                                                      ║
## ╚════════════════════════════════════════════════════════════════════════════════╝

  - name: "api-redirect-to-play"
    match:
    - uri:
        exact: "/goto/admission-android"
    redirect:
      redirectCode: 307
      scheme: https
      authority: play.google.com
      uri: "/store/apps/details?id=com.stagedates.admission"

  - name: "api-redirect-to-apple"
    match:
    - uri:
        exact: "/goto/admission-ios"
    redirect:
      redirectCode: 307
      scheme: https
      authority: apps.apple.com
      uri: "/de/app/stagedates/6466404674"

  - name: "api-redirect-to-sitemap-bucket"
    match:
    - uri:
        exact: "/seo/sitemap.xml"
    redirect:
      redirectCode: 307
      scheme: https
      authority: storage.googleapis.com
      uri: "/stdts-prod-cdn-bucket/seo/sitemap.xml"
---
apiVersion: networking.istio.io/v1beta1
kind: ServiceEntry
metadata:
  name: storage-googleapis-com
  namespace: stdts-dev
  labels:
    istio.io/rev: 1-23-3
spec:
  hosts:
    - storage.googleapis.com
  ports:
    - number: 443
      name: https
      # protocol: TLS
      protocol: HTTPS
  resolution: DNS
  location: MESH_EXTERNAL

# ************************************************************************************************** #
