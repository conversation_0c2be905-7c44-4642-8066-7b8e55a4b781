# This service exports the opentelemetry collector for the cr workloads.
# It is used to collect the traces from the cr over the VPC connection.
# Key components:
# metadata.annotations: cloud.google.com/load-balancer-type = Internal
# type: LoadBalancer
---
apiVersion: v1
kind: Service
metadata:
  annotations:
    cloud.google.com/neg: '{"ingress":true}'
    cloud.google.com/load-balancer-type: "Internal"
  labels:
    app: opentelemetry-collector
  name: opentelemetry-collector-cr
  namespace: opentelemetry
spec:
  ipFamilies:
  - IPv4
  ipFamilyPolicy: SingleStack
  ports:
  - name: otel-grpc-cr
    port: 4317
    protocol: TCP
    targetPort: 4317
  - name: otlp-http-cr
    port: 4318
    protocol: TCP
    targetPort: 4318
  selector:
    app: opentelemetry-collector
  sessionAffinity: None
  type: LoadBalancer
