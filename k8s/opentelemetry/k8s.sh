#!/bin/bash

# ╔════════════════════════════════════════════════════════════════════════════════╗
# ║ Version:                                                                    02 ║
# ║ Date of Version:                                                    27.06.2025 ║
# ║ Owner:                                                                      SD ║
# ║ Classification:                                                       Internal ║
# ║ Distribution:                                                        All Staff ║
# ╚════════════════════════════════════════════════════════════════════════════════╝

# THIS SCRIPT is a PoC and content needs to be moved to Terraform:
# 1. Create a service account
# 2. Bind roles to the service account
# 3. Map the GCP SA to the GKE KSA in order for Workload Identity to work properly

export LOGFILE="${PWD}/out/install.${ENV}.log"
export GCLOUD_PROJECT="stdts-dev"
export NSOTC=opentelemetry
export NS="-n $NSOTC"
export MANIFEST="yaml/${ENV}/manifest.yaml"

function requirements_ksa(){
    kubectl $CONTEXT auth can-i --list
    kubectl $CONTEXT get componentstatuses
}

# TODO: move the following PoC towards Terraform environment
function requirements_iam(){
    gcloudx iam service-accounts create opentelemetry-collector --project=${GCLOUD_PROJECT} 2>&1 | tee -a ${LOGFILE}
    gcloudx projects add-iam-policy-binding ${GCLOUD_PROJECT} --member "serviceAccount:opentelemetry-collector@${GCLOUD_PROJECT}.iam.gserviceaccount.com" --role "roles/logging.logWriter" --condition=None 2>&1 | tee -a ${LOGFILE}
    gcloudx projects add-iam-policy-binding ${GCLOUD_PROJECT} --member "serviceAccount:opentelemetry-collector@${GCLOUD_PROJECT}.iam.gserviceaccount.com" --role "roles/monitoring.metricWriter" --condition=None 2>&1 | tee -a ${LOGFILE}
    gcloudx projects add-iam-policy-binding ${GCLOUD_PROJECT} --member "serviceAccount:opentelemetry-collector@${GCLOUD_PROJECT}.iam.gserviceaccount.com" --role "roles/cloudtrace.agent" --condition=None 2>&1 | tee -a ${LOGFILE}

    gcloudx iam service-accounts add-iam-policy-binding opentelemetry-collector@${GCLOUD_PROJECT}.iam.gserviceaccount.com \
        --role roles/iam.workloadIdentityUser \
        --member "serviceAccount:${GCLOUD_PROJECT}.svc.id.goog[opentelemetry/opentelemetry-collector]" \
        --project ${GCLOUD_PROJECT}
}

function add_export_service_to_vpc(){
    printf "Hint:\nOptionally, you might want to export the opentelemetry collector service to the VPC, so that it is accessible from cloud run. This is optional, but it is recommended.\n\n"
    printf "\$ kubectl \$CONTEXT -n\$NSOTC apply -f \$MANIFEST  --validate=false 2>&1 | tee -a ${LOGFILE}\n\n"
    printf "Connection string for Dev: OTEL_EXPORTER_OTLP_ENDPOINT=\"http://otel.gke.internal:30667\"\n"
    printf "Connection string for Prod: OTEL_EXPORTER_OTLP_ENDPOINT=\"http://otel.gke.internal:32628\"\n"
    printf "For port number details please check the service description in the GCP console or on the cluster.\n"
}

# TODO: enhance the PoC
# 1. migrate the following procedure to a helm chart
# 2. start considering the environment switch properly
# 3. take care of the collector's setup at the heart of the manifest (maybe by extracting it to sth like collector-setup.yaml for higher portability?)
function create(){
    false

    # export GCLOUD_PROJECT=<your project id> # should also contained in the main .env.dev
    kubectl $CONTEXT create namespace $NSOTC

    kubectl kustomize https://github.com/GoogleCloudPlatform/otlp-k8s-ingest/k8s/base | envsubst > $MANIFEST

    echo "now refine the collector's config, adjust the config map to your needs. here is a getting-started example:"
    echo wget https://raw.githubusercontent.com/GoogleCloudPlatform/otlp-k8s-ingest/refs/heads/main/config/collector.yaml

    kubectl $CONTEXT -n$NSOTC apply -f $MANIFEST  --validate=false 2>&1 | tee -a ${LOGFILE}
    echo "then reload/restart the collector"
    for i in 0 1; do
        echo kubectl $CONTEXT -n$NSOTC scale --replicas=$i deployment/opentelemetry-collector
    done

    echo "Now add to your workloads the env var:"
    echo OTEL_EXPORTER_OTLP_ENDPOINT="http://opentelemetry-collector.opentelemetry.svc.cluster.local:4318"
    echo "Now go to ./verify/4_verify-otlp-tracing-working.sh"

    add_export_service_to_vpc
}

function verify_setup(){
  printf "\$ kubectl $CONTEXT get po,all -nopentelemetry\n"
}
