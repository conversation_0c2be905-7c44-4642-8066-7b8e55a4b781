# ╔════════════════════════════════════════════════════════════════════════════════╗
# ║ Version:                                                                   0.2 ║
# ║ Date of Version:                                                    23.04.2024 ║
# ║ Owner:                                                                      SD ║
# ║ Classification:                                                       Internal ║
# ║ Distribution:                                                        All Staff ║
# ╚════════════════════════════════════════════════════════════════════════════════╝

---
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

image:
  repository: unleashorg/unleash-edge
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion
  tag: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations: {}

podLabels: {}

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  # Supported types: ClusterIP, NodePort, LoadBalancer
  type: ClusterIP
  port: 3063
  # target port will be set to port if not set
  targetPort: ""
  # nodePort is optional and only used when service.type is NodePort or LoadBalancer
  nodePort: ""

  annotations:
    cloud.google.com/neg: '{"ingress": true}'
    cloud.google.com/backend-config: '{"ports": {"3063":"gc-backendconfig-unleash-edge"}}'

resources:
  # {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # requests:
  #   cpu: 100m
  #   memory: 64Mi
  # limits:
  #   cpu: 200m
  #   memory: 64Mi
  requests:
    cpu: 200m
    memory: 64Mi
  limits:
    cpu: 400m
    memory: 64Mi


# sd: 2024-04-15
ingress:
  enabled: false
  className: "nginx"
  annotations: {}
    # kubernetes.io/ingress.class: nginx
  # kubernetes.io/tls-acme: "true"
  hosts:
    #- host: chart-example.local
    #- host: unleash.dev.stagedates.it
    - host: flags.prod.stagedates.it
    # TODO: flags.services.stagedates.com
      paths:
        - path: /
          pathType: ImplementationSpecific
  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #

startupProbe:
  {}
  # exec:
  #   command: ["/unleash-edge", "ready"]
  # initialDelaySeconds: 10
  # timeoutSeconds: 10
  # periodSeconds: 10
  # successThreshold: 1
  # failureThreshold: 10

livenessProbe:
  exec:
# sd  
#    command: ["/unleash-edge", "health"]
# sd
  httpGet:
    path: /internal-backstage/health
    port: http
  initialDelaySeconds: 10
  timeoutSeconds: 10
  periodSeconds: 10
  successThreshold: 1
  failureThreshold: 5

readinessProbe:
  exec:
# sd  
#    command: ["/unleash-edge", "health"]
# sd  
#    command: ["/unleash-edge", "health"]
  httpGet:
    path: /internal-backstage/health
    port: http
  initialDelaySeconds: 10
  timeoutSeconds: 10
  periodSeconds: 10
  successThreshold: 1
  failureThreshold: 1

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 4
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

nodeSelector: {}

tolerations: []

affinity: {}

topologySpreadConstraints: {}
  # - maxSkew: 1
  #   topologyKey: topology.kubernetes.io/zone
  #   whenUnsatisfiable: DoNotSchedule

# Adds environment variables
# sd: 2024-04-12
# env: []
env: []

volumes: []
# - name: secrets-store-inline
#   mountPath: "/mnt/secrets-store"

volumeMounts: []
# - name: secrets-store-inline
#   mountPath: "/mnt/secrets-store"
#   readOnly: true

extraObjects: []
#  - apiVersion: secrets-store.csi.x-k8s.io/v1
#    kind: SecretProviderClass
#    metadata:
#      name: unleash-edge-secret-provider
#    spec:
#      provider: aws
#      parameters:
#        objects: |
#            - objectName: "unleash-token"
#              objectType: "secretsmanager"
#              jmesPath:
#                - path: apiToken
#                  objectAlias: apiToken
#      secretObjects:
#      - secretName: unleash-secret
#        type: Opaque
#        data:
#        - objectName: apiToken
#          key: apiToken

# adds environmentvars for existing secrets to the container via tpl function
existingSecrets: 
  ""
  # - name: TOKENS
  #   valueFrom:
  #     secretKeyRef:
  #       name: unleash-edge-authz-client-token
  #       key: token
  # - name: TOKENS
  #   valueFrom:
  #     secretKeyRef:
  #       name: secretname
  #       key: secretkey

edge:
  upstreamUrl: "http://unleash.unleash:4242"
  logLevel: "warn"
  logFormat: "json"
  rustBacktrace: "full"

# RUST_LOG="warn,unleash_edge=debug" # "warn" | "debug" | "warn,unleash_edge=debug"
# UNLEASH_URL # value: https://flags.prod.stagedates.it
# RUST_BACKTRACE=1 # 1 | full

