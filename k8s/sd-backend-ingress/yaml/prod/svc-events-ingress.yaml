---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: events
  namespace: stdts-prod
  labels:
    app.kubernetes.io/name: events
  annotations:
    kubernetes.io/ingress.class: "gce"
    kubernetes.io/ingress.global-static-ip-name: "sd-address-api-events"
    ingress.kubernetes.io/ssl-redirect: "true"
    kubernetes.io/ingress.allow-http: "true"
    ingress.kubernetes.io/hsts-max-age: "0"
    cert-manager.io/cluster-issuer: "letsencrypt-production"
    acme.cert-manager.io/http01-edit-in-place: "true"
    cert-manager.io/subject-organizations: "Stagedates"
    cert-manager.io/subject-organizationalunits: "Stagedates"
spec:
  ingressClassName: gce
  tls:
  - secretName: tls-pem-events
    hosts:
        - events.prod.stagedates.it
  defaultBackend:
    service:
      name: events-gke
      port:
        number: 8080

  rules:
  - host: events.prod.stagedates.it
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: events-gke
            port:
              number: 8080
