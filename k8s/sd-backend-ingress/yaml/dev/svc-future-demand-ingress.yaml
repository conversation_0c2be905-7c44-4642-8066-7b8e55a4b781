---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: future-demand
  namespace: stdts-dev
  labels:
    app.kubernetes.io/name: future-demand
  annotations:
    kubernetes.io/ingress.class: "gce"
    kubernetes.io/ingress.global-static-ip-name: "sd-address-api-future-demand"
    ingress.kubernetes.io/ssl-redirect: "true"
    kubernetes.io/ingress.allow-http: "true"
    ingress.kubernetes.io/hsts-max-age: "0"
    cert-manager.io/cluster-issuer: "letsencrypt-production"
    acme.cert-manager.io/http01-edit-in-place: "true"
    cert-manager.io/subject-organizations: "Stagedates"
    cert-manager.io/subject-organizationalunits: "Stagedates"
spec:
  ingressClassName: gce
  tls:
  - secretName: tls-pem-future-demand
    hosts:
        - future-demand.dev.stagedates.it
  defaultBackend:
    service:
      name: future-demand-gke
      port:
        number: 8080

  rules:
  - host: future-demand.dev.stagedates.it
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: future-demand-gke
            port:
              number: 8080
      # - path: /sssc
      #   pathType: Prefix
      #   backend:
      #     service:
      #       name: carts-gke
      #       port:
      #         number: 8080
  # - host: sssc.dev.stagedates.it
  #   http:
  #     paths:
  #     - path: /
  #       pathType: Prefix
  #       backend:
  #         service:
  #           name: carts-gke
  #           port:
  #             number: 8080
