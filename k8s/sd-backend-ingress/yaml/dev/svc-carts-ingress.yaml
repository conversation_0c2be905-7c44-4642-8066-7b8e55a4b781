---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: carts
  namespace: stdts-dev
  labels:
    app.kubernetes.io/name: carts
  annotations:
    kubernetes.io/ingress.class: "gce"
    kubernetes.io/ingress.global-static-ip-name: "sd-address-api-carts"
    ingress.kubernetes.io/ssl-redirect: "true"
    kubernetes.io/ingress.allow-http: "true"
    ingress.kubernetes.io/hsts-max-age: "0"
    cert-manager.io/cluster-issuer: "letsencrypt-staging"
    acme.cert-manager.io/http01-edit-in-place: "true"
    cert-manager.io/subject-organizations: "Stagedates"
    cert-manager.io/subject-organizationalunits: "Stagedates"
spec:
  ingressClassName: gce
  tls:
  - secretName: tls-pem-carts
    hosts:
        - carts.dev.stagedates.it
  defaultBackend:
    service:
      name: carts-gke
      port:
        number: 8080

  rules:
  - host: carts.dev.stagedates.it
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: carts-gke
            port:
              number: 8080
