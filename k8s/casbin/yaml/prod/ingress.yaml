---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: casdoor
  namespace: casdoor
  labels:
    app.kubernetes.io/name: casdoor
  annotations:
    kubernetes.io/ingress.class: "gce"
    kubernetes.io/ingress.global-static-ip-name: "sd-address-auth"
    ingress.kubernetes.io/ssl-redirect: "true"
    ingress.kubernetes.io/hsts-max-age: "0"
    cert-manager.io/cluster-issuer: "letsencrypt-production"
    acme.cert-manager.io/http01-edit-in-place: "true"
    cert-manager.io/subject-organizations: "Stagedates"
    cert-manager.io/subject-organizationalunits: "Stagedates"
    kubernetes.io/ingress.allow-http: "true"
    ingress.gcp.kubernetes.io/allow-cors: "true"
    ingress.gcp.kubernetes.io/cors-allow-origin: "*"
    ingress.gcp.kubernetes.io/cors-allow-methods: "GET, POST, DELETE, OPTIONS"
    ingress.gcp.kubernetes.io/cors-allow-headers: "Content-Type,Authorization"
spec:
  ingressClassName: gce
  tls:
  - secretName: tls-pem-casdoor
    hosts:
        - auth.prod.stagedates.it
  defaultBackend:
    service:
      name: casdoor
      port:
        number: 8000

  rules:
  - host: auth.prod.stagedates.it
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: casdoor
            port:
              number: 8000

