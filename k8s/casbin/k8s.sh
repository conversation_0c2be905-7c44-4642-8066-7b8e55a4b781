#!/bin/bash

# ╔════════════════════════════════════════════════════════════════════════════════╗
# ║ Version:                                                                    05 ║
# ║ Date of Version:                                                    29.04.2025 ║
# ║ Owner:                                                                      SD ║
# ║ Classification:                                                       Internal ║
# ║ Distribution:                                                        All Staff ║
# ╚════════════════════════════════════════════════════════════════════════════════╝

set -eu -o pipefail
set -o posix
# set -o errexit   # abort on nonzero exitstatus
# set -o nounset   # abort on unbound variable
# set -o pipefail  # don't hide errors within pipes

script_path=$(realpath "${BASH_SOURCE[0]}")
script_path="${script_path%/*}"
cd "${script_path}"

NS=casdoor
export VERSION="v1.894.0" # 1.604.0

# source "${0}.env"

{
    function usage(){
        printf "Usage:\n"
        printf "\t%s <subcommand>\n\n" ${0}
        printf "Subcommands:\n"
        printf "\t< %s | %s | %s | %s >\n" "--create" "--app-url-internal" "--get" "--upgrade"
    }


   function create_helm(){
        # IMPORTANT: Helm chart is broken at development time :-/
        # IMPORTANT: fall back to simple manifest deployment at ./yaml/*yaml for now
        # IMPORTANT: check if helm chart is usable again, implement, and move back to helm chart mode.

        printf "helm --kube-context %s install -f /yaml/%s/values.yaml casdoor oci://registry-1.docker.io/casbin/casdoor-helm-charts --create-namespace --namespace %s 2>&1 | tee -a out/install.log\n" "${KUBE_CTX}" "${ENV}" "${NS}"

    }

   function upgrade_helm(){
        printf "# Fetch missing subcharts:\nhelm --kube-context %s dependencies update --namespace %s 2>&1 | tee -a out/%s/upgrade.log\n" "${KUBE_CTX}" "${NS}" "${ENV}"
        printf "# Upgrade:\nhelm --kube-context %s upgrade --install -f ./yaml/%s/values.yaml --version %s casdoor oci://registry-1.docker.io/casbin/casdoor-helm-charts --create-namespace --namespace %s 2>&1 | tee -a out/%s/upgrade.log\n" "${KUBE_CTX}" "${ENV}" "${VERSION}" "${NS}" "${ENV}"
    }

    # DEPRECATED - Goto: create_helm()
    function create_yaml(){
        printf "kubectl --context=%s -n %s apply -f ./yaml/%s/routing-block-service.yaml 2>&1 | tee -a out/%s/install.log\n" "${KUBE_CTX}" "${NS}" "${ENV}" "${ENV}"
        printf "kubectl --context=%s -n %s apply -f ./yaml/%s/ingress.yaml 2>&1 | tee -a out/%s/install.log\n" "${KUBE_CTX}" "${NS}" "${ENV}" "${ENV}"
    }

    function forward_ports(){
        export POD_NAME=$(kubectl --context="${KUBE_CTX}" get pods --namespace casdoor -l "app.kubernetes.io/name=casdoor,app.kubernetes.io/instance=casdoor" -o jsonpath="{.items[0].metadata.name}")
        export CONTAINER_PORT=$(kubectl --context="${KUBE_CTX}" get pod --namespace casdoor "${POD_NAME}" -o jsonpath="{.spec.containers[0].ports[0].containerPort}")
        echo "Visit http://127.0.0.1:8080 to use your application"
        kubectl --context="${KUBE_CTX}" --namespace casdoor port-forward "${POD_NAME}" "8080:${CONTAINER_PORT}"
    }

    function get(){
        printf "kubectl --context=%s get endpoints block-service -n %s\n" "${KUBE_CTX}" "${NS}"

        printf "kubectl --context=%s get all -n %s\n" "${KUBE_CTX}" "${NS}"
    }

    function test(){

        printf "# Test internally (bypass Ingress)\n# Should return 404
BLOCK_IP=\$(kubectl get svc block-service -n casdoor -o jsonpath='{.spec.clusterIP}')
kubectl run -n casdoor -it --rm test --image=curlimages/curl -- \
  curl -v http://\$BLOCK_IP/hello/world/service\n\n"

printf "# Test externally (after GCE updates)\n# Should return 404
curl -v https://auth.dev.stagedates.it/hello/world/service\n\n"


        printf "kubectl --context=%s get endpoints block-service -n %s\n" "${KUBE_CTX}" "${NS}"

        printf "kubectl --context=%s get all -n %s\n" "${KUBE_CTX}" "${NS}"
    }
         
    # TODO: Adjust according to your needs and remove this block
    function main(){
        case ${1-usage} in
            --create)
                create_helm
                echo
                create_yaml
            ;;

            --upgrade)
                upgrade_helm
            ;;

            --get)
                get
            ;;

            --test)
                test
            ;;
            
            --forward-ports)
                forward_ports
            ;;

            --app-url-internal)
                get_internal_app_url
            ;;

            *)
                usage
            ;;
        esac
    }
}

{
    main "${@}"
    exit 0
}
