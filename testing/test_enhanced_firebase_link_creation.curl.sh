#!/bin/bash

# Test script for enhanced Firebase link creation handler
# Tests both scenarios: with and without srcUrl provided

API_KEY="your-api-key-here"
BASE_URL="http://localhost:8080"

echo "Testing Enhanced Firebase Link Creation Handler"
echo "=============================================="

# Test 1: Create link without srcUrl (should generate path and srcUrl)
echo -e "\n1. Testing link creation WITHOUT srcUrl (should generate path and srcUrl):"
curl -X POST "${BASE_URL}/links" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: ${API_KEY}" \
  -d '{
    "target_url": "https://stagedates.com/events/test-event-123"
  }' | jq '.'

# Test 2: Create link with srcUrl (should extract path from srcUrl)
echo -e "\n2. Testing link creation WITH srcUrl (should extract path from srcUrl):"
curl -X POST "${BASE_URL}/links" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: ${API_KEY}" \
  -d '{
    "shortlink": "https://stagedates.com/abc123",
    "target_url": "https://stagedates.com/events/test-event-456"
  }' | jq '.'

# Test 3: Create link with full srcUrl (should extract path from URL)
echo -e "\n3. Testing link creation WITH full srcUrl (should extract path):"
curl -X POST "${BASE_URL}/links" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: ${API_KEY}" \
  -d '{
    "shortlink": "https://stagedates.com/custom-path",
    "target_url": "https://stagedates.com/events/test-event-789"
  }' | jq '.'

echo -e "\nTest completed!"
echo "Expected results:"
echo "- All responses should include 'id', 'shortlink', 'target_url', 'path', 'inserted_at', 'updated_at'"
echo "- Test 1: Should have generated path (6 hex chars) and constructed srcUrl"
echo "- Test 2: Should have path 'abc123' extracted from srcUrl"
echo "- Test 3: Should have path 'custom-path' extracted from srcUrl"
