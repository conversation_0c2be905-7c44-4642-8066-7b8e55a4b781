#!/bin/bash

# Test script for the modernized shortlink API
# This script demonstrates the new API features including validation and error handling

BASE_URL="http://localhost:8080"
API_KEY="your-api-key-here"

echo "=== Testing Modernized Shortlink API ==="
echo

# Test 1: Valid request with target URL only
echo "Test 1: Creating link with target URL only"
curl -X POST "$BASE_URL/links" \
  -H "Content-Type: application/json" \
  -H "X-Api-Key: $API_KEY" \
  -d '{
    "targetUrl": "https://example.com/very/long/path/to/resource"
  }' \
  -w "\nHTTP Status: %{http_code}\n" \
  -s
echo -e "\n"

# Test 2: Valid request with custom short URL
echo "Test 2: Creating link with custom short URL"
curl -X POST "$BASE_URL/links" \
  -H "Content-Type: application/json" \
  -H "X-Api-Key: $API_KEY" \
  -d '{
    "targetUrl": "https://example.com/custom-resource",
    "shortlink": "https://stagedates.com/my-custom-link"
  }' \
  -w "\nHTTP Status: %{http_code}\n" \
  -s
echo -e "\n"

# Test 3: Validation error - missing target URL
echo "Test 3: Validation error - missing target URL"
curl -X POST "$BASE_URL/links" \
  -H "Content-Type: application/json" \
  -H "X-Api-Key: $API_KEY" \
  -d '{
    "shortlink": "https://stagedates.com/missing-target"
  }' \
  -w "\nHTTP Status: %{http_code}\n" \
  -s
echo -e "\n"

# Test 4: Validation error - invalid target URL
echo "Test 4: Validation error - invalid target URL"
curl -X POST "$BASE_URL/links" \
  -H "Content-Type: application/json" \
  -H "X-Api-Key: $API_KEY" \
  -d '{
    "targetUrl": "not-a-valid-url"
  }' \
  -w "\nHTTP Status: %{http_code}\n" \
  -s
echo -e "\n"

# Test 5: Validation error - invalid short URL
echo "Test 5: Validation error - invalid short URL"
curl -X POST "$BASE_URL/links" \
  -H "Content-Type: application/json" \
  -H "X-Api-Key: $API_KEY" \
  -d '{
    "targetUrl": "https://example.com/valid",
    "shortlink": "not-a-valid-url"
  }' \
  -w "\nHTTP Status: %{http_code}\n" \
  -s
echo -e "\n"

# Test 6: Unauthorized - missing API key
echo "Test 6: Unauthorized - missing API key"
curl -X POST "$BASE_URL/links" \
  -H "Content-Type: application/json" \
  -d '{
    "targetUrl": "https://example.com/unauthorized"
  }' \
  -w "\nHTTP Status: %{http_code}\n" \
  -s
echo -e "\n"

# Test 7: Invalid JSON format
echo "Test 7: Invalid JSON format"
curl -X POST "$BASE_URL/links" \
  -H "Content-Type: application/json" \
  -H "X-Api-Key: $API_KEY" \
  -d '{
    "targetUrl": "https://example.com/valid"
    "missing": "comma"
  }' \
  -w "\nHTTP Status: %{http_code}\n" \
  -s
echo -e "\n"

# Test 8: Duplicate short URL (run this twice to test duplicate detection)
echo "Test 8a: Creating link with specific short URL"
curl -X POST "$BASE_URL/links" \
  -H "Content-Type: application/json" \
  -H "X-Api-Key: $API_KEY" \
  -d '{
    "targetUrl": "https://example.com/first",
    "shortlink": "https://stagedates.com/duplicate-test"
  }' \
  -w "\nHTTP Status: %{http_code}\n" \
  -s
echo -e "\n"

echo "Test 8b: Attempting to create duplicate short URL"
curl -X POST "$BASE_URL/links" \
  -H "Content-Type: application/json" \
  -H "X-Api-Key: $API_KEY" \
  -d '{
    "targetUrl": "https://example.com/second",
    "shortlink": "https://stagedates.com/duplicate-test"
  }' \
  -w "\nHTTP Status: %{http_code}\n" \
  -s
echo -e "\n"

echo "=== Testing Complete ==="
echo
echo "Expected Results:"
echo "- Tests 1-2: Should return 201 with link data"
echo "- Tests 3-5: Should return 400 with structured validation errors:"
echo "  {\"errorCode\": \"VALIDATION_ERROR\", \"message\": \"...\", \"data\": [...]}"
echo "- Test 6: Should return 401 with unauthorized error:"
echo "  {\"errorCode\": \"UNAUTHORIZED\", \"message\": \"Invalid or missing API key\"}"
echo "- Test 7: Should return 400 with JSON parsing error"
echo "- Test 8a: Should return 201 with link data"
echo "- Test 8b: Should return 409 with duplicate error:"
echo "  {\"errorCode\": \"DUPLICATE_LINK\", \"message\": \"A link with this short URL already exists\"}"
