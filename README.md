# Short Links Service

A Go-based service for managing and redirecting short URLs to their original destinations.

## API

### Short link creation API

The link creation endpoint is API key protected.

There are two ways for creation short link for now. See the following examples for more details:

```bash
  curl -k -X POST $BASE_URL/links \
  -H "Content-Type: application/json" \
  -H "X-Api-Key: f0e1abbd-XXX-XXX-XXX-22d19a11230f" \
  -d '{
    "targetUrl": "https://stagedates.com/events/star-fm-rooftop-party-nhow-berlin-20250726-vDO1Q"
  }'
```

```bash
  curl -k -X POST $BASE_URL/links \
  -H "Content-Type: application/json" \
  -H "X-Api-Key: f0e1abbd-XXX-XXX-XXX-22d19a11230f" \
  -d '{
    "targetUrl": "https://stagedates.com/events/star-fm-rooftop-party-nhow-berlin-20250726-vDO1Q",
    "shortlink": "https://link.dev.stagedat.es/abcd"
  }'
```

... where BASE_URL equals https://link.dev.stagedat.es for now, on dev environment:

```bash
BASE_URL="https://link.dev.stagedat.es"
```

## Middleware and components in use

### Web Framework & Core

- [gofr](https://gofr.dev/) - Go web framework with built-in observability

### Messaging & Event Processing

- [Google Cloud Pub/Sub](https://cloud.google.com/pubsub) - Message queue for event processing
- [Redis](https://github.com/redis/go-redis) - In-memory data store for caching

### Security & Session Management

- [SCS Session Manager](https://github.com/alexedwards/scs) - HTTP session management
- [NoSurf](https://github.com/justinas/nosurf) - CSRF protection middleware
- [JWT](https://github.com/golang-jwt/jwt) - JSON Web Token authentication (via gofr)

### Database & Storage

- [PostgreSQL Driver](https://github.com/lib/pq) - PostgreSQL database connectivity (via gofr)

### Observability & Monitoring

- [Prometheus](https://prometheus.io/) - Metrics collection and monitoring (via gofr)
- [OpenTelemetry](https://opentelemetry.io/) - Distributed tracing and observability (via gofr)

### Utilities

- [Google UUID](https://github.com/google/uuid) - UUID generation for unique identifiers

## Troubleshooting

### Database: Workaround for search_path on PostgreSQL

gofr.dev doesn't currently support overriding `postgresql` connection parameters or options like `search_path`. However, this is needed in order to isolate this application at schema level. Therefore the following workaround is recommended:

```sql
ALTER ROLE shortlinksvc SET search_path TO shortlink;
```

## Running the Service Locally

This project uses the `start.sh` script to simplify common development tasks. Here's how to get the service running:

1. **Explore Script Options:**

```bash
./start.sh --help
```

2.  **Start Essential Background Services:**

```bash
./start.sh --start-docker-compose-env
```

3.  **Run the Short Link Application:**

```bash
./start.sh --start-local-short-link-service
```
