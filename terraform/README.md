# This Project Folder

This folder manages core resources, globally shared components and resouce descriptions the stagedates platforms consist in or that are crucial for the operation of the platforms. See, for instance, the following incomplete list:

* Github organization, repositories, teams, users, relations (Sub-project candidate)
* Managed GCP resources
    * Access management and sensitive configuration (Saas) (Sub-project candidate)
        * IAM policies
        * Secret Manager
    * CloudSQL instances (Saas)
    * PubSub topics and subscriptions (Saas)
    * Cloud Cloud Run services (Saas)
    * Cloud Build triggers (Saas)
    * Artifact Registry repositories (Saas)
    * ~~Cloud Functions~~ (this is delegated to `cloud-functions` repository)
    * GKE (PaaS)
        * Cluster management
        * `Stagedates` Backend Service infrastructure (Sub-project candidate)
        * Ingress objects
        * Istio Ingress Gateway
        * Istio Service Mesh
        * ...
    * Network (PaaS)
        * VPCs
        * IP Addresses
        * Subnets
        * Firewalls
        * Routes
        * NAT Gateways
        * Load Balancers
        * Network Peerings / PSA services
        * ...
    * ...

# HowTo use this code

## Setup the environment


| File | Context                                                                                                                                                                                                                           |
| --- |-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| credentials/credentials.tfrc.json | Terraform Cloud Workspace Integration - At runtime this file must be located at `$HOME/.terraform.d/credentials.tfrc.json`. Alternatively create a symlink to `/home/<USER>/.config/gcloud/application_default_credentials.json` |
| .env | Project specific environment vars (see .env.template) for  more details                                                                                                                                                           |

## Install requirements

Use the contained .tool-versions file for [asdf](https://asdf-vm.com/) for instance to install the tooling required:

1. Terraform
1. Terragrunt

## Provision Resources

Before working with the terraform code provided in this folder, make sure to have the required permissions and credentials to access the resources. Also make sure you are connected with the proper context. 

(Make sure to run all commands from this folder (`foundation/terraform`).)

Refresh your Google Cloud credentials if necessary:

```bash
gcloud auth login
gcloud auth application-default login
```

Update your kubernetes context to point to the correct cluster, eg.:

```bash
kubectl config use-context gke_stdts-dev_europe-west3_sd-a01
```

After running `make init` (see below) check the terraform workspace you are in:

```bash
terraform workspace show
```

### Provision backend services

```bash
$ make help
USAGE:

1. init the environment:
	source .env.ENV
	make init

2. Static service tasks to re-provision a backend service infra components:
	make svc-accounts
	make svc-carts
	make svc-emails
	make svc-events
	make svc-fulfillment
	make svc-future-demands
	make svc-orders
	make svc-reports


2. Dynamic tasks:
	< ACTION=<action> COMPONENT=<component> SVC=<svc> make magic [-n] | make graph >


WHERE:
	action:		apply | import | destroy
	component:	cloudrun | triggers | secrets | pubsub | datastream | secretmanager
	svc:		orders | events | com | accounts | main | globals

	graph:		generates a terraform state graph according to https://developer.hashicorp.com/terraform/cli/commands/graph

EXAMPLE:
	ACTION=apply COMPONENT=cloudrun SVC=emails make magic [-n]

```

# Troubleshooting


**E:** = "Possible errors like"

**S:** = "Possible solutions"

----

## OAuth2 errors

**E:**

```bash
Error: Invalid function argument
...
credentials = file("./credentials/gcp-project-develop.json")
```

```bash
│ Error: Post "https://.*googleapis.com/...": oauth2: cannot fetch token: Post "https://oauth2.googleapis.com/token": dial tcp X.X.X.X:443: i/o timeout
```

**S:**

```bash
gcloud auth login
gcloud auth application-default login
```

---

## Github token errors

**E:**

```bash

Error: GET https://api.github.com/user: 401 Bad credentials []
...
   with provider["registry.terraform.io/integrations/github"],
   on github.tf line nn, in provider "github":
...
```

**S:**

Validate and/or replace the content of `var.github_token` in the `secrets.*tfvars` files with a valid github token.

---

## GKE authentication errors

**E:**

```bash
Error: Error getting cluster credentials: timed out waiting for the condition
...
```

```bash
ERROR: (gcloud.container.clusters.update) ResponseError: code=400, message=Node XXX "pool-name" requires recreation ...
```

**S:**

 look for the CURRENT column:
```bash
kubectl config get-contexts
```

further troubleshooting:
```bash
kubectl $CONTEXT cluster-info
kubectl config view
kubectl config get-clusters
kubectl config use-context <context-name>
```

If nothing helps, try to re-create/refresh the $HOME/.kube/config file:
```bash
# backup and remove the file
mv $HOME/.kube/config $HOME/.kube/config.backup
# ... and or refresh the file content sections
gcloud container clusters get-credentials sd-a01 --region europe-west3 --project stdts-prod
```

---

## GCP services : Cloud Run

**E:**

```bash
│ Error: Error waiting to create Service: Error waiting for Creating Service: Error code NN, message: Revision 'service-name-revision-name' is not ready and cannot serve traffic.... PORT=NNN environment variable...
│ 
│ Logs URL: https://console.cloud.google.com/logs/...
│ 
│   with google_cloud_run_v2_service.XXX-service-name-XXX,
│   on [svc-service-name.tf](http://svc-service-name.tf/) line NNN, in resource "google_cloud_run_v2_service" "XXX-service-name-XXX":
│   84: resource "google_cloud_run_v2_service" "XXX-service-name-XXX" {
```

**S:**

This mostly indicates that the service description code is erroneous. Go through the code and check the following:

* The service is missing environment variables or secrets.
* The service name is unique across the project.
* The revision name is unique across the project.
* The service is not already deployed.
* The service is not already in the state file.
* The service is not completely defined in the code.

---

**E:**

```bash
Error: Error creating Service: googleapi: Error 409: Resource 'redacted' already exists

```

**S:**

In this case, delete the cloud run instance and re-provision it.

CAUTION: this will delete the instance and all the data associated with it, and cause a temporary disruption of the service. Follow the procedure and notify the team and stakeholders before proceeding.
