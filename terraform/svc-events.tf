// ╔════════════════════════════════════════════════════════════════════════════════╗
// ║ Version:                                                                    17 ║
// ║ Date of Version:                                                    09.05.2025 ║
// ║ Owner:                                                                      SD ║
// ║ Classification:                                                       Internal ║
// ║ Distribution:                                                        All Staff ║
// ╚════════════════════════════════════════════════════════════════════════════════╝

//  _____                 _         ____           
// | ____|_   _____ _ __ | |_ ___  / ___|_   _____ 
// |  _| \ \ / / _ \ '_ \| __/ __| \___ \ \ / / __|
// | |___ \ V /  __/ | | | |_\__ \  ___) \ V / (__ 
// |_____| \_/ \___|_| |_|\__|___/ |____/ \_/ \___|
//  

locals {
  events_svc = {
    service_name                        = "events-service"
    service_suffix                      = "events"
    service_suffix_capitals             = "EVENTS"
    service_suffix_uppercase            = "EVENTS"
    environment                         = var.environment
    default_log_level                   = var.default_log_level
    cloudbuild_file                     = var.cloudbuild_file
    container_image                     = var.service_images["events"]
    container_port                      = "4000"
    github_owner                        = var.github_owner
    db_cloudsql_instances               = var.db_cloudsql_instances
    cloud_run_service_account           = var.cloud_run_service_account
    managed_by_terraform_label_short    = var.managed_by_terraform_label_short
    managed_by_terraform_label          = var.managed_by_terraform_label_short_lowercase
    service_ci_trigger_branch           = var.events_service_ci_trigger_branch
    github_organization_uri             = var.github_organization_uri
    service_ci_service_account          = var.ci_service_account
    service_ci_trigger_branch           = var.events_service_ci_trigger_branch
    platform_version                    = var.platform_version_current
    vpc_connector                       = var.vpc_connector
    project                             = var.project
    region                              = var.region
    gke_url_events                      = var.gke_url_events
    frontend_url_events                 = var.frontend_url_events
    backend_url_events                  = var.backend_url_events
    shortlink_url_gke-internal          = var.shortlink_url_gke-internal
    shortlink_url_vpc-internal          = var.shortlink_url_vpc-internal
    
    casdoor_url_events                  = var.casdoor_url
    casdoor_organization_name_events    = var.casdoor_organization_name
    casdoor_promoter_domain_name_events = var.casdoor_promoter_domain_name
    casdoor_user_model_name_events      = var.casdoor_user_model_name
    casdoor_user_enforcer_name_events   = var.casdoor_user_enforcer_name

    pubsub_imported_tickets_topic_name        = var.events_service_pubsub_imported_tickets_topic_name
    pubsub_imported_tickets_orders_topic_name = var.orders_service_pubsub_imported_tickets_topic_name
    pubsub_imported_tickets_subscription_name = var.events_service_pubsub_imported_tickets_subscription_name

    // Performance settings:
    min_scaling_instance_count = var.min_scaling_instance_count
    max_scaling_instance_count = var.max_scaling_instance_count
    resources_cpu_idle         = var.resources_cpu_idle
    startup_cpu_boost          = var.startup_cpu_boost
    resources_limits_cpu       = var.resources_limits_cpu_events
    resources_limits_memory    = var.resources_limits_memory

    debug_image = "europe-west3-docker.pkg.dev/stdts-dev/sd-devops-builder/helloworld:latest"
  }
}


# ╔════════════════════════════════════════════════════════════════════════════════╗
# ║ The Cluster Components                                                         ║
# ╚════════════════════════════════════════════════════════════════════════════════╝

module "k8s-a01-service-events" {
  service_name         = "events"
  nodepool_label       = "backend"
  cluster_name         = local.gke-main.cluster_name
  service_type         = "ClusterIP"
  image                = var.service_images["events"]
  label_elixir_cluster = "backend-api"

  hpa_max_replicas = 2
  hpa_min_replicas = 1

  service_port        = "8080"
  service_port_name   = "tcp-events"
  service_target_port = "4000"

  readiness_path            = "/events/api/health"
  readiness_port            = "4000"
  readiness_initial_delay   = "20"
  readiness_period_seconds  = "10"
  readiness_timeout_seconds = "10"

  liveness_path                  = "/events/api/health"
  liveness_port                  = "4000"
  liveness_initial_delay_seconds = "20"
  liveness_period_seconds        = "10"
  liveness_timeout_seconds       = "10"

  resources_limits_cpu      = "1.0"
  resources_limits_memory   = "2048Mi"
  resources_requests_cpu    = "250m"
  resources_requests_memory = "300Mi"

  project          = var.project
  region           = local.gke-main.region
  platform_version = local.gke-main.platform_version

  source = "./modules/gke_service_hpa"

  depends_on = [
    //	module.gke-main
  ]
}

module "config-map-events" {
  name = "events"
  data = {
    SERVICE_NAME                           = "events-service",
    STAGE_DATE_BACKEND                     = var.host_name_main,
    FRONTEND_URL                           = var.frontend_url_events,
    BACKEND_URL                            = var.backend_url_events,
    SHORTLINK_URL                          = local.events_svc.shortlink_url_gke-internal,
    GKE_URL                                = var.gke_url_events,
    GCLOUD_PROJECT                         = local.gke-main.project,
    ENVIRONMENT                            = var.environment,
    LOG_LEVEL                              = var.default_log_level,
    MAIL_PUBSUB_TOPIC                      = "email.email",
    PDF_PUBSUB_TOPIC                       = "generate-pdf-v1",
    IMPORTED_TICKETS_PUBSUB_TOPIC          = local.events_svc.pubsub_imported_tickets_topic_name,
    IMPORTED_TICKETS_PUBSUB_SUBSCRIPTION   = local.events_svc.pubsub_imported_tickets_subscription_name,
    TICKET_SUBSCRIPTION_NAME               = "events-worker.orders.tickets",
    POSTGRES_SCHEMA                        = "events",
    FUTURE_DEMAND_EVENTS_SUBSCRIPTION_NAME = "events-worker.future-demand.events"
    TICKET_CATEGORIES_PUBSUB_TOPIC         = "events.ticket_categories",
    SALES_CHANNELS_PUBSUB_TOPIC            = "events.sales_channels",
    OTEL_EXPORTER_OTLP_ENDPOINT            = var.otel_exporter_otlp_endpoint,
    CASDOOR_URL                            = var.casdoor_url,
    CASDOOR_ORGANIZATION_NAME              = var.casdoor_organization_name,
    CASDOOR_PROMOTER_DOMAIN_NAME           = var.casdoor_promoter_domain_name,
    CASDOOR_USER_MODEL_NAME                = var.casdoor_user_model_name,
    CASDOOR_USER_ENFORCER_NAME             = var.casdoor_user_enforcer_name
  }

  label_managed_by_terraform = "terraform"
  platform_version           = local.gke-main.platform_version

  project = local.gke-main.project
  source  = "./modules_main/config-map-events"
}

# ╔════════════════════════════════════════════════════════════════════════════════╗
# ║ Cloud Run                                                                      ║
# ╚════════════════════════════════════════════════════════════════════════════════╝
// TODO: TF upgrade: Provision or import module.events-cr-minimal: pubsub_ticket_categories_topic_name
module "events-cr-minimal" {
  name = "events-service"

  service_suffix_uppercase                  = "EVENTS"
  service_suffix                            = "events"
  container_image                           = local.events_svc.container_image
  backend_hostname                          = var.fqdn
  secret_key_base                           = var.secret_key_base_global
  pubsub_email_topic_name                   = "email.email"
  pubsub_email_subscription_name            = "generate-pdf-v1"
  pubsub_ticket_categories_topic_name       = "events.ticket_categories"
  pubsub_imported_tickets_topic_name        = local.events_svc.pubsub_imported_tickets_topic_name
  pubsub_imported_tickets_subscription_name = local.events_svc.pubsub_imported_tickets_subscription_name
  environment                               = local.events_svc.environment
  default_log_level                         = local.events_svc.default_log_level

  github_owner            = var.github_owner
  github_organization_uri = var.github_organization_uri

  db_cloudsql_instances = var.db_cloudsql_instances

  cloud_run_service_account = var.cloud_run_service_account
  ci_service_account        = local.events_svc.service_ci_service_account

  vpc_connector              = var.vpc_connector
  project                    = var.project
  region                     = var.region
  platform_version           = var.platform_version_v22
  managed_by_terraform_label = var.managed_by_terraform_label_short_lowercase

  // Performance settings:
  min_scaling_instance_count          = local.events_svc.min_scaling_instance_count
  max_scaling_instance_count          = local.events_svc.max_scaling_instance_count
  resources_cpu_idle                  = local.events_svc.resources_cpu_idle
  startup_cpu_boost                   = local.events_svc.startup_cpu_boost
  resources_limits_cpu                = local.events_svc.resources_limits_cpu
  resources_limits_memory             = local.events_svc.resources_limits_memory
  frontend_url_events                 = local.events_svc.frontend_url_events
  backend_url_events                  = local.events_svc.backend_url_events
  shortlink_url                       = local.events_svc.shortlink_url_vpc-internal
  casdoor_url_events                  = local.events_svc.casdoor_url_events
  casdoor_organization_name_events    = local.events_svc.casdoor_organization_name_events
  casdoor_promoter_domain_name_events = local.events_svc.casdoor_promoter_domain_name_events
  casdoor_user_model_name_events      = local.events_svc.casdoor_user_model_name_events
  casdoor_user_enforcer_name_events   = local.events_svc.casdoor_user_enforcer_name_events
  gke_url_events                      = local.events_svc.gke_url_events

  source = "./modules_main/svc-events-minimal"
}

module "events-secrets" {
  secrets = [
    "POSTGRES_DB",
    "POSTGRES_HOST",
    "POSTGRES_PASSWORD",
    "POSTGRES_PORT",
    "POSTGRES_USER",
    "SERVICE_CLIENT_ID",
    "SERVICE_CLIENT_SECRET",
    "BACKEND_API_TOKEN",
    "SECRET_KEY_BASE",
    "SCHEDULER_TOKEN",
    "SERVICE_ACCOUNT",
    "TICKET_SUBSCRIPTION_NAME",
    "CASDOOR_CERTIFICATE",
    "CASDOOR_APPLICATION_ID",
    "CASDOOR_APPLICATION_SECRET",
    "CASDOOR_USERNAME",
    "CASDOOR_PASSWORD",
  ]

  global_shared_secrets = []

  service_name             = "events-service"
  platform_version         = var.platform_version_v24Q1
  service_suffix           = "events"
  service_suffix_uppercase = "EVENTS"

  project = var.project
  region  = var.region

  managed_by_terraform_label = "terraform"

  source = "./modules/secrets"
}

module "events-secrets-casdoor" {
  secrets = [
    "CASDOOR_CERTIFICATE",
    "CASDOOR_APPLICATION_ID",
    "CASDOOR_APPLICATION_SECRET",
    "CASDOOR_USERNAME",
    "CASDOOR_PASSWORD",
    "CASDOOR_USER_ENFORCER_NAME"
  ]

  global_shared_secrets = []

  service_name             = "events-service"
  platform_version         = var.platform_version_v24Q1
  service_suffix           = "events"
  service_suffix_uppercase = "EVENTS"

  project = var.project
  region  = var.region

  managed_by_terraform_label = "terraform"

  source = "./modules/secrets"
}

module "events-secrets-unleash" {
  secrets = [
    "UNLEASH_URL",
    "UNLEASH_APP_NAME",
    "UNLEASH_INSTANCE_ID",
    "UNLEASH_AUTH_TOKEN"
  ]

  global_shared_secrets = []

  service_name             = "events-service"
  platform_version         = var.platform_version_v25Q1
  service_suffix           = "events"
  service_suffix_uppercase = "EVENTS"

  project = var.project
  region  = var.region

  managed_by_terraform_label = "terraform"

  source = "./modules/secrets"
}

module "events-secrets-geocoding" {
  secrets = [
    "GOOGLE_GEOCODING_API_KEY"
  ]

  global_shared_secrets = []

  service_name             = "events-service"
  platform_version         = var.platform_version_v25Q1
  service_suffix           = "events"
  service_suffix_uppercase = "EVENTS"

  project = var.project
  region  = var.region

  managed_by_terraform_label = "terraform"

  source = "./modules/secrets"
}

# ╔════════════════════════════════════════════════════════════════════════════════╗
# ║ Build and CI                                                                   ║
# ╚════════════════════════════════════════════════════════════════════════════════╝


module "ci-triggers-events" {
  project                          = local.project
  region                           = local.region
  service_name                     = "events"
  cloudbuild_file                  = local.events_svc.cloudbuild_file
  service_ci_service_account       = local.service_ci_service_account
  github_owner                     = local.github_owner
  github_organization_uri          = local.github_organization_uri
  service_ci_trigger_branch        = local.service_ci_trigger_branch
  managed_by_terraform_label_short = local.managed_by_terraform_label
  source                           = "./modules/ci_triggers"
}

# ╔════════════════════════════════════════════════════════════════════════════════╗
# ║ Pub / Sub                                                                      ║
# ╚════════════════════════════════════════════════════════════════════════════════╝

// TODO: import + apply
module "pub-sub-events-ticket-categories_topic" {
  topic_name = "events.ticket_categories"

  region  = var.region
  project = var.project

  source = "./modules/pubsub-topic"
}
module "pub-sub-events-sales-channels_topic" {
  topic_name = "events.sales_channels"
  region     = var.region
  project    = var.project
  source     = "./modules/pubsub-topic"
}
module "pub-sub-events-imported-tickets_topic" {
  topic_name = local.events_svc.pubsub_imported_tickets_topic_name
  region     = var.region
  project    = var.project
  source     = "./modules/pubsub-topic"
}
module "pub-sub-events-imported-tickets_subscriber" {
  subscription_name    = local.events_svc.pubsub_imported_tickets_subscription_name
  topic_name           = local.events_svc.pubsub_imported_tickets_orders_topic_name
  ack_deadline_seconds = local.project == "stdts-prod" ? 10 : local.project == "stdts-dev" ? 180 : 60

  platform_version = var.platform_version_v24Q4
  region           = var.region
  project          = var.project

  source = "./modules/pubsub-subscription"
}
module "pub-sub-events" {
  subscription_name    = "events-worker.orders.tickets"
  topic_name           = "orders.tickets"
  ack_deadline_seconds = local.project == "stdts-prod" ? 10 : local.project == "stdts-dev" ? 180 : 60

  platform_version = var.platform_version_v24Q4
  region           = var.region
  project          = var.project

  source = "./modules/pubsub-subscription"
}

module "pub-sub-fd-events" {
  subscription_name = "events-worker.future-demand.events"
  topic_name        = "future-demand.events"

  platform_version = var.platform_version_v24Q1
  region           = var.region
  project          = var.project

  source = "./modules/pubsub-subscription"
}

# ╔════════════════════════════════════════════════════════════════════════════════╗
# ║ Cloud Scheduler Jobs                                                           ║
# ╚════════════════════════════════════════════════════════════════════════════════╝

module "events-finalize-events-scheduler" {
  name        = "events-finalize-events-scheduler"
  description = "Triggers event finalization. Closes event - ready for booking."

  schedule                = "13 * * * *"
  paused                  = false
  attempt_deadline        = "180s"
  http_target_url         = "https://${var.api_url_host_name}/events/api/events/scripts/${var.event_scheduler_finalization_target_script_id}/status"
  http_target_http_method = "POST"
  http_target_body        = base64encode("{\"action\": \"execute\"}")
  http_target_headers     = { "Content-Type" = "application/json", "User-Agent" = "Google-Cloud-Scheduler" }

  project          = var.project
  platform_version = var.platform_version_current

  source = "./modules/cloud_scheduler_job"
}

module "events-sync-tickets-counter-scheduler" {
  name        = "events-sync-tickets-counter-scheduler"
  description = "Triggers the ticket counter synchronisation - Syncs count from orders svc to events svc."

  schedule                = "24 2 * * *"
  paused                  = false
  attempt_deadline        = "180s"
  http_target_url         = "https://${var.api_url_host_name}/events/api/events/scripts/${var.event_scheduler_sync_tickets_counter_target_script_id}/status"
  http_target_http_method = "POST"
  http_target_body        = base64encode("{\"action\": \"execute\"}")
  http_target_headers     = { "Content-Type" = "application/json", "User-Agent" = "Google-Cloud-Scheduler" }

  project          = var.project
  platform_version = var.platform_version_current

  source = "./modules/cloud_scheduler_job"
}

module "events-sync-tracking-links-stats-scheduler" {
  name        = "events-sync-tracking-links-stats-scheduler"
  description = "Cloud build scheduler to trigger tracking links synchronization"

  schedule                = "15 * * * *"
  paused                  = false
  attempt_deadline        = "300s"
  http_target_url         = "https://${var.api_url_host_name}/events/api/events/scripts/${var.event_scheduler_tracking_links_stats_target_script_id}/status"
  http_target_http_method = "POST"
  http_target_body        = base64encode("{\"action\": \"execute\"}")
  http_target_headers     = { "Content-Type" = "application/json", "User-Agent" = "Google-Cloud-Scheduler" }

  project          = var.project
  platform_version = var.platform_version_current

  source = "./modules/cloud_scheduler_job"
}

module "events-delete-expired-channels-scheduler" {
  name        = "events-delete-expired-channels"
  description = "Cloud build scheduler to delete expired channels"

  schedule                = "* * * * *"
  paused                  = true
  attempt_deadline        = "300s"
  http_target_url         = "https://${var.api_url_host_name}/events/api/scripts"
  http_target_http_method = "POST"
  http_target_body        = base64encode("{\"action\":\"delete_expired_channels\",\"token\": \"${var.events_scheduler_expired_channels_token}\"}")
  http_target_headers     = { "Content-Type" = "application/json", "User-Agent" = "Google-Cloud-Scheduler" }

  retry_config_min_backoff_duration = "5s"
  retry_config_max_retry_duration   = "0s"
  retry_config_max_doublings        = "5"
  retry_config_retry_count          = "0"

  project          = var.project
  platform_version = var.platform_version_current

  source = "./modules/cloud_scheduler_job"
}

module "events-artifact-registry" {
  name = "events-service"

  project                    = local.events_svc.project
  region                     = local.events_svc.region
  managed_by_terraform_label = local.events_svc.managed_by_terraform_label
  platform_version           = local.events_svc.platform_version

  source = "./modules/gcp_artifact_registry"
}

