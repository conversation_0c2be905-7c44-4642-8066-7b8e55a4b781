// ╔════════════════════════════════════════════════════════════════════════════════╗
// ║ Version:                                                                    16 ║
// ║ Date of Version:                                                    30.01.2025 ║
// ║ Owner:                                                                      SD ║
// ║ Classification:                                                       Internal ║
// ║ Distribution:                                                        All Staff ║
// ╚════════════════════════════════════════════════════════════════════════════════╝

project = "stdts-prod"

region_zone = [
  "europe-west3-c",
  "europe-west3-b"
]

db_settings_tier                    = "db-custom-4-8192"
db_database_version                 = "POSTGRES_14"
db_settings_maintenance_window_day  = 2
db_settings_maintenance_window_hour = 4
db_region_zone_secondary            = "europe-west3-a"
database_port_main                  = 5432
database_port_replica               = 5433
ssl_domains                         = ["api.stagedates.com"]
vpc_connector                       = "projects/stdts-prod/locations/europe-west3/connectors/stdts-vpc-connector-prod"
api_host_accounts                   = "www.stagedates.com"
api_host_com                        = "www.stagedates.com"
api_host_orders                     = "www.stagedates.com"
api_host_events                     = "www.stagedates.com"

iam_backend_team_roles = [
  "projects/stdts-prod/roles/customRoleSecretsRO",
  "projects/stdts-prod/roles/CustomRoleItSysRegistryRO",
  "projects/stdts-prod/roles/CustomRoleBackendDev",
  "roles/cloudbuild.builds.viewer",
  "roles/cloudbuild.builds.editor",
  "roles/cloudscheduler.jobRunner",
  "roles/run.viewer",
  "roles/monitoring.viewer",
  "roles/secretmanager.viewer",
  "roles/resourcemanager.tagViewer",
  "roles/bigquery.resourceViewer",
  "roles/compute.viewer",
  "roles/secretmanager.viewer",
  "roles/secretmanager.secretVersionAdder",
  "roles/secretmanager.secretVersionManager",
  "roles/viewer"
]

iam_operations_team_roles = [
  "roles/cloudbuild.builds.viewer",
  "roles/run.viewer",
  "roles/cloudsql.viewer",
  "roles/monitoring.viewer",
  "roles/secretmanager.viewer",
  "roles/resourcemanager.tagViewer",
  "roles/bigquery.resourceViewer",
  "roles/compute.viewer"
]

iam_frontend_team_roles = [
  "projects/stdts-prod/roles/CustomRoleItSysRegistryRO",
  "projects/stdts-prod/roles/CustomRoleMobileFrontend",
  "projects/stdts-prod/roles/CustomRoleWebFrontend",
  "roles/firebase.admin",
  "roles/firebase.developAdmin",
  "roles/firebase.growthAdmin",
  "roles/firebase.qualityAdmin",
  "roles/firebase.viewer"
]

db_name_prefix = "sb"

mail_pubsub_topic_orders                                 = "email.email"
pdf_pubsub_topic_cap_orders                              = "generate-pdf-v1"
backend_base_url_orders                                  = "https://stagedates.com/api"
orders_service_pubsub_imported_tickets_topic_name        = "orders.imported_tickets"
orders_service_pubsub_imported_tickets_subscription_name = "orders.events-worker.imported_tickets"
events_service_pubsub_imported_tickets_topic_name        = "events.imported_tickets"
events_service_pubsub_imported_tickets_subscription_name = "events.orders-worker.imported_tickets"

frontend_url_orders       = "https://stagedates.com"
frontend_url_emails       = "https://stagedates.com"
frontend_url_events       = "https://stagedates.com"
frontend_url_reports      = "https://stagedates.com"
frontend_url_accounts     = "https://stagedates.com"
frontend_url_com          = "https://stagedates.com"
frontend_url_fulfillment  = "https://stagedates.com"
frontend_url_waiting-room = "https://stagedates.com"
backend_url_orders        = "https://stagedates.com"
backend_url_emails        = "https://stagedates.com"
backend_url_events        = "https://stagedates.com"
backend_url_reports       = "https://stagedates.com"
backend_url_accounts      = "https://stagedates.com"
backend_url_com           = "https://stagedates.com"
backend_url_fulfillment   = "https://stagedates.com"
backend_url_waiting-room  = "https://stagedates.com"

gke_url_orders = "https://api.stagedates.com"
gke_url_events = "https://api.stagedates.com"

firebase_base_url_accounts = "https://identitytoolkit.googleapis.com/v1"
firebase_issuer_accounts   = "https://securetoken.google.com/stdts-prod"


accounts_service_ci_trigger_branch = "main"
com_service_ci_trigger_branch      = "main"
orders_service_ci_trigger_branch   = "main"
events_service_ci_trigger_branch   = "main"
dummy_service_ci_trigger_branch    = "main"
emails_service_ci_trigger_branch   = "main"
reports_service_ci_trigger_branch  = "main"
web_ui_service_ci_trigger_branch   = "master"

// TODO: put mesh service endpoint when service mesh ready to rumble
api_host_emails = "emails"

// node_pool_machine_type_<POOL>
node_pool_machine_type_backend = "n1-standard-8"
node_pool_machine_type_infra   = "n2-standard-4"

static_pages_legal_pages_domains = ["https://static.stagedates.com", "https://www.stagedates.com", "https://stagedates.com"]
static_pages_legal_pages_host    = ["static.stagedates.com."]
for_dotted_domains               = ["static.stagedates.com."] // rather use only one. multiple domains broke the cert.


message_retention_duration = "604800s"

// Performance - Cloud Run
min_scaling_instance_count  = 1
max_scaling_instance_count  = 5
startup_cpu_boost           = true
resources_cpu_idle          = false
resources_limits_cpu_events = "4000m"

host_name_main    = "stagedates.com"
api_url_host_name = "api.stagedates.com"

environment       = "prod"
default_log_level = "info"

future_demand_service_auth_api_url             = "https://vendor.future-demand.com"
future_demand_service_client_api_url           = "https://client-api.prd.future-demand.com/api/v1"
future_demand_service_webhook_api_url          = "https://webhook.prd.future-demand.com/stagedates"
future_demand_service_partner_email            = "<EMAIL>"
future_demand_service_event_subscription_name  = "future-demand-worker.events.events"
future_demand_service_orders_subscription_name = "future-demand-worker.orders.orders"
future_demand_service_casdoor_url              = "http://casdoor.casdoor.svc.cluster.local:8000"
future_demand_service_unleash_url              = "http://unleash-edge.unleash.svc.cluster.local:3063"

shortlink_url_gke-internal              = "http://shortlink-gke.stdts-prod.svc.cluster.local:8080"
shortlink_url_vpc-internal              = "http://************:8080"
casdoor_redirect_uri                    = "https://stagedates.com/accounts/api/auth/casdoor/callback"
casdoor_url                             = "https://auth.prod.stagedates.it"
casdoor_authorize_url                   = "https://auth.prod.stagedates.it/login/oauth/authorize"
casdoor_token_url                       = "https://auth.prod.stagedates.it/api/login/oauth/access_token"
casdoor_user_enforcer_name              = "user_enforcer"
casdoor_organization_name               = "stagedates"
casdoor_security_role_name              = "security"
casdoor_seller_role_name                = "seller"
casdoor_booking_office_application_name = "booking_office_app"
casdoor_certificate_id                  = "admin/cert-built-in"
casdoor_promoter_domain_name            = "promoter"
casdoor_user_model_name                 = "user_model"

profanity_service_event_subscription_name  = "profanity-worker.events.events"
profanity_service_orders_subscription_name = "profanity-worker.orders.orders"
profanity_service_casdoor_url              = "http://casdoor.casdoor.svc.cluster.local:8000"
profanity_service_com_subscription_name    = "profanity-worker.com.message"

orders_service_casdoor_url                  = "https://auth.prod.stagedates.it"
orders_service_casdoor_application_enforcer = "stagedates/application_enforcer"
orders_service_casdoor_user_enforcer        = "built-in/user_enforcer"

orders_service_unleash_url           = "https://flags.prod.stagedates.it/api"
orders_service_unleash_app_name      = "orders-service"
orders_service_unleash_instance_name = "orders-service-prod"

entrance_change_ticket_swap_mail = "<EMAIL>"


notification_channel_userscope_id = {
  "mt"              = "1689824204277768768"
  "db"              = "7453232205586537354"
  "jv"              = "10205548357385877893"
  "tk"              = "5500307035298750493"
  "monitoring_mail" = "11777356190966659184"
}

service_hostnames_internal = {
  "accounts"      = "accounts-gke.stdts-prod.svc.cluster.local"
  "carts"         = "carts-gke.stdts-prod.svc.cluster.local"
  "com"           = "com-gke.stdts-prod.svc.cluster.local"
  "dummy"         = "dummy-gke.stdts-prod.svc.cluster.local"
  "emails"        = "emails-gke.stdts-prod.svc.cluster.local"
  "events"        = "events-gke.stdts-prod.svc.cluster.local"
  "future_demand" = "future-demand-gke.stdts-prod.svc.cluster.local"
  "orders"        = "orders-gke.stdts-prod.svc.cluster.local"
  "reports"       = "reports-gke.stdts-prod.svc.cluster.local"
  "profanity"     = "profanity-gke.stdts-prod.svc.cluster.local"
  "waiting_room"  = "waiting-rooms-gke.stdts-prod.svc.cluster.local"
}
service_hostnames_external = {
  "accounts"      = "api.stagedates.com"
  "carts"         = "api.stagedates.com"
  "com"           = "api.stagedates.com"
  "dummy"         = "api.stagedates.com"
  "emails"        = "api.stagedates.com"
  "events"        = "api.stagedates.com"
  "future_demand" = "api.stagedates.com"
  "orders"        = "api.stagedates.com"
  "reports"       = "api.stagedates.com"
  "profanity"     = "api.stagedates.com"
  "waiting_room"  = "api.stagedates.com"
}

service_endpoint_internal = {
  "accounts"      = "/accounts/api"
  "carts"         = "/carts/api"
  "com"           = "/coms/api"
  "dummy"         = "/dummy/api"
  "emails"        = "/emails/api"
  "events"        = "/events/api"
  "future_demand" = "/future-demand/api"
  "orders"        = "/orders/api"
  "reports"       = "/reports/api"
  "profanity"     = "/profanity/api"
  "waiting_room"  = "/waiting-rooms/api"
}

service_endpoint_external = {
  "accounts"      = "/accounts/api"
  "carts"         = "/carts/api"
  "com"           = "/coms/api"
  "dummy"         = "/dummy/api"
  "emails"        = "/emails/api"
  "events"        = "/events/api"
  "future_demand" = "/future-demand/api"
  "orders"        = "/orders/api"
  "reports"       = "/reports/api"
  "profanity"     = "/profanity/api"
  "waiting_room"  = "/waiting-rooms/api"
}

cloudrun_url_backendv2 = "https://dev.stagedat.es" # TODO: set the new api url?
external_database_ip   = "************"
db_cloudsql_instances  = "stdts-prod:europe-west3:sb-db-1"

service_images = {
  accounts = "europe-west3-docker.pkg.dev/stdts-prod/accounts-service/accounts-service:latest"
  com      = "europe-west3-docker.pkg.dev/stdts-prod/com-service/com-service:latest"
  emails   = "europe-west3-docker.pkg.dev/stdts-prod/emails-service/emails-service:latest"
  events   = "europe-west3-docker.pkg.dev/stdts-prod/events-service/events-service:latest"
  orders   = "europe-west3-docker.pkg.dev/stdts-prod/orders-service/orders-service:latest"
  reports  = "europe-west3-docker.pkg.dev/stdts-prod/reports-service/reports-service:latest"
}
