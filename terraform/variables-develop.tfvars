// ╔════════════════════════════════════════════════════════════════════════════════╗
// ║ Version:                                                                    14 ║
// ║ Date of Version:                                                    30.01.2025 ║
// ║ Owner:                                                                      SD ║
// ║ Classification:                                                       Internal ║
// ║ Distribution:                                                        All Staff ║
// ╚════════════════════════════════════════════════════════════════════════════════╝

project = "stdts-dev"

region_zone = [
  "europe-west3-c",
  "europe-west3-b"
]


db_settings_tier                    = "db-custom-2-8192"
db_database_version                 = "POSTGRES_14"
db_settings_maintenance_window_day  = 2
db_settings_maintenance_window_hour = 4
db_region_zone_secondary            = "europe-west3-a"

database_port_main    = 5432
database_port_replica = 5433

ssl_domains = ["api.dev.stagedates.it", "api.dev.stagedat.es"]

vpc_connector     = "projects/stdts-dev/locations/europe-west3/connectors/stdts-vpc-connector-dev"
api_host_accounts = "dev.stagedat.es"
api_host_com      = "dev.stagedat.es"
api_host_orders   = "dev.stagedat.es"
api_host_events   = "dev.stagedat.es"
// TODO: put mesh service endpoint when service mesh ready to rumble
api_host_emails = "emails"

iam_backend_team_roles    = []
iam_operations_team_roles = []
iam_frontend_team_roles = [
  "projects/stdts-dev/roles/CustomRoleItSysRegistryRO",
  "roles/firebase.admin",
  "roles/firebase.developAdmin",
  "roles/firebase.growthAdmin",
  "roles/firebase.qualityAdmin",
  "roles/firebase.viewer"
]

db_name_prefix = "sd"

mail_pubsub_topic_orders                                 = "email.email"
pdf_pubsub_topic_cap_orders                              = "generate-pdf-v1"
backend_base_url_orders                                  = "https://dev.stagedat.es/api"
orders_service_pubsub_imported_tickets_topic_name        = "orders.imported_tickets"
orders_service_pubsub_imported_tickets_subscription_name = "orders.events-worker.imported_tickets"
events_service_pubsub_imported_tickets_topic_name        = "events.imported_tickets"
events_service_pubsub_imported_tickets_subscription_name = "events.orders-worker.imported_tickets"

frontend_url_orders       = "https://dev.stagedat.es"
frontend_url_emails       = "https://dev.stagedat.es"
frontend_url_events       = "https://dev.stagedat.es"
frontend_url_reports      = "https://dev.stagedat.es"
frontend_url_accounts     = "https://dev.stagedat.es"
frontend_url_com          = "https://dev.stagedat.es"
frontend_url_fulfillment  = "https://dev.stagedat.es"
frontend_url_waiting-room = "https://dev.stagedat.es"
backend_url_orders        = "https://api.dev.stagedat.es"
backend_url_emails        = "https://api.dev.stagedat.es"
backend_url_events        = "https://api.dev.stagedat.es"
backend_url_reports       = "https://api.dev.stagedat.es"
backend_url_accounts      = "https://api.dev.stagedat.es"
backend_url_com           = "https://api.dev.stagedat.es"
backend_url_fulfillment   = "https://api.dev.stagedat.es"
backend_url_waiting-room  = "https://api.dev.stagedat.es"

gke_url_orders            = "https://api.dev.stagedat.es"
gke_url_events            = "https://api.dev.stagedat.es"

accounts_service_ci_trigger_branch = "fusion"
com_service_ci_trigger_branch      = "fusion"
orders_service_ci_trigger_branch   = "fusion"
events_service_ci_trigger_branch   = "fusion"
dummy_service_ci_trigger_branch    = "fusion"
emails_service_ci_trigger_branch   = "fusion"
reports_service_ci_trigger_branch  = "fusion"
web_ui_service_ci_trigger_branch   = "fusion"


static_pages_legal_pages_domains = ["https://static.dev.stagedat.es", "https://dev.stagedat.es"]
static_pages_legal_pages_host    = ["static.dev.stagedat.es."]
for_dotted_domains               = ["static.dev.stagedat.es."] // rather use only one. multiple domains broke the cert.
cloud_run_service_account        = "<EMAIL>"
firebase_base_url_accounts       = "https://identitytoolkit.googleapis.com/v1"
firebase_issuer_accounts         = "https://securetoken.google.com/stdts-dev"

node_pool_machine_type_backend = "n1-standard-4"
node_pool_machine_type_infra   = "n1-standard-4"

message_retention_duration = "259200s"

// Performance - Cloud Run
min_scaling_instance_count = 1
max_scaling_instance_count = 1
startup_cpu_boost          = true
resources_cpu_idle         = false

host_name_main    = "dev.stagedat.es"
api_url_host_name = "api.dev.stagedat.es"

environment       = "dev"
default_log_level = "debug"


future_demand_service_auth_api_url             = "https://vendor.future-demand.com"
future_demand_service_client_api_url           = "https://client-api.stg.future-demand.com/api/v1"
future_demand_service_webhook_api_url          = "https://webhook.prd.future-demand.com/test-stagedates"
future_demand_service_partner_email            = "<EMAIL>"
future_demand_service_event_subscription_name  = "future-demand-worker.events.events"
future_demand_service_orders_subscription_name = "future-demand-worker.orders.orders"
future_demand_service_casdoor_url              = "http://casdoor.casdoor.svc.cluster.local:8000"
future_demand_service_unleash_url              = "https://flags.prod.stagedates.it"

shortlink_url_gke-internal              = "http://shortlink-gke.stdts-dev.svc.cluster.local:8080"
shortlink_url_vpc-internal              = "http://*************:8080"
casdoor_redirect_uri                    = "https://dev.stagedat.es/accounts/api/auth/casdoor/callback"
casdoor_url                             = "http://casdoor.casdoor.svc.cluster.local:8000"
casdoor_authorize_url                   = "https://auth.dev.stagedates.it/login/oauth/authorize"
casdoor_token_url                       = "https://auth.dev.stagedates.it/api/login/oauth/access_token"
casdoor_user_enforcer_name              = "user_enforcer"
casdoor_organization_name               = "stagedates"
casdoor_security_role_name              = "security"
casdoor_seller_role_name                = "seller"
casdoor_booking_office_application_name = "booking_office_app"
casdoor_certificate_id                  = "admin/cert-built-in"
casdoor_promoter_domain_name            = "promoter"
casdoor_user_model_name                 = "user_model"

profanity_service_event_subscription_name  = "profanity-worker.events.events"
profanity_service_com_subscription_name    = "profanity-worker.com.message"
profanity_service_orders_subscription_name = "profanity-worker.orders.orders"
profanity_service_casdoor_url              = "http://casdoor.casdoor.svc.cluster.local:8000"

orders_service_casdoor_url                  = "http://casdoor.casdoor.svc.cluster.local:8000"
orders_service_casdoor_application_enforcer = "stagedates/application_enforcer"
orders_service_casdoor_user_enforcer        = "built-in/user_enforcer"

orders_service_unleash_url           = "https://flags.prod.stagedates.it/api"
orders_service_unleash_app_name      = "orders-service"
orders_service_unleash_instance_name = "orders-service-dev"

notification_channel_userscope_id = {
  "mt"              = "2151763286272576322"
  "db"              = "12658764948721723846"
  "jv"              = "14185743843294288474"
  "tk"              = "6461495951469747799"
  "monitoring_mail" = "9999898952475431823"
}

entrance_change_ticket_swap_mail = "<EMAIL>"


# ╔═════════════════════════════════════════════════════════════════════════════════════════╗
# ║ Global Service Discovery - Internal and external host name matrix for Global config map ║
# ╚═════════════════════════════════════════════════════════════════════════════════════════╝

service_hostnames_internal = {
  "accounts"      = "accounts-gke.stdts-dev.svc.cluster.local"
  "carts"         = "carts-gke.stdts-dev.svc.cluster.local"
  "com"           = "com-gke.stdts-dev.svc.cluster.local"
  "dummy"         = "dummy-gke.stdts-dev.svc.cluster.local"
  "emails"        = "emails-gke.stdts-dev.svc.cluster.local"
  "events"        = "events-gke.stdts-dev.svc.cluster.local"
  "future_demand" = "future-demand-gke.stdts-dev.svc.cluster.local"
  "orders"        = "orders-gke.stdts-dev.svc.cluster.local"
  "reports"       = "reports-gke.stdts-dev.svc.cluster.local"
  "profanity"     = "profanity-gke.stdts-dev.svc.cluster.local"
  "waiting_room"  = "waiting-rooms-gke.stdts-dev.svc.cluster.local"
}

service_hostnames_external = {
  "accounts"      = "api.dev.stagedates.it"
  "carts"         = "api.dev.stagedates.it"
  "com"           = "api.dev.stagedates.it"
  "dummy"         = "api.dev.stagedates.it"
  "emails"        = "api.dev.stagedates.it"
  "events"        = "api.dev.stagedates.it"
  "future_demand" = "api.dev.stagedates.it"
  "orders"        = "api.dev.stagedates.it"
  "reports"       = "api.dev.stagedates.it"
  "profanity"     = "api.dev.stagedates.it"
  "waiting_room"  = "api.dev.stagedates.it"
}

service_endpoint_internal = {
  "accounts"      = "/accounts/api"
  "carts"         = "/carts/api"
  "com"           = "/coms/api"
  "dummy"         = "/dummy/api"
  "emails"        = "/emails/api"
  "events"        = "/events/api"
  "future_demand" = "/future-demand/api"
  "orders"        = "/orders/api"
  "reports"       = "/reports/api"
  "profanity"     = "/profanity/api"
  "waiting_room"  = "/waiting-rooms/api"
}

service_endpoint_external = {
  "accounts"      = "/accounts/api"
  "carts"         = "/carts/api"
  "com"           = "/coms/api"
  "dummy"         = "/dummy/api"
  "emails"        = "/emails/api"
  "events"        = "/events/api"
  "future_demand" = "/future-demand/api"
  "orders"        = "/orders/api"
  "reports"       = "/reports/api"
  "profanity"     = "/profanity/api"
  "waiting_room"  = "/waiting-rooms/api"
}

cloudrun_url_backendv2 = "https://dev.stagedat.es" # TODO: set the new api url?
external_database_ip   = "************"
db_cloudsql_instances  = "stdts-dev:europe-west3:sd-db-1"

service_images = {
  accounts = "europe-west3-docker.pkg.dev/stdts-dev/accounts-service/accounts-service:latest"
  com      = "europe-west3-docker.pkg.dev/stdts-dev/com-service/com-service:latest"
  emails   = "europe-west3-docker.pkg.dev/stdts-dev/emails-service/emails-service:latest"
  events   = "europe-west3-docker.pkg.dev/stdts-dev/events-service/events-service:latest"
  orders   = "europe-west3-docker.pkg.dev/stdts-dev/orders-service/orders-service:latest"
  reports  = "europe-west3-docker.pkg.dev/stdts-dev/reports-service/reports-service:latest"
}

cloudbuild_file = "infrastructure/cloudbuild_dev.yaml"
