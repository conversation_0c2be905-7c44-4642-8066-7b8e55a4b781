# ╔════════════════════════════════════════════════════════════════════════════════╗
# ║ Version:                                                                    04 ║
# ║ Date of Version:                                                    09.01.2025 ║
# ║ Owner:                                                                      SD ║
# ║ Classification:                                                       Internal ║
# ║ Distribution:                                                        All Staff ║
# ╚════════════════════════════════════════════════════════════════════════════════╝

# ╔════════════════════════════════════════════════════════════════════════════════╗
# ║ Backend Developers                                                             ║
# ╚════════════════════════════════════════════════════════════════════════════════╝


resource "google_project_iam_member" "be_db" {
    for_each = var.iam_backend_team_roles
    project = var.project
    role    = each.value
    member  = "user:<EMAIL>"
}
resource "google_project_iam_member" "be_jv" {
    for_each = var.iam_backend_team_roles
    project = var.project
    role    = each.value
    member  = "user:<EMAIL>"
}
resource "google_project_iam_member" "be_lk" {
    for_each = var.iam_backend_team_roles
    project = var.project
    role    = each.value
    member  = "user:<EMAIL>"
}
resource "google_project_iam_member" "be_mt" {
    for_each = var.iam_backend_team_roles
    project = var.project
    role    = each.value
    member  = "user:<EMAIL>"
}
resource "google_project_iam_member" "be_nd" {
    for_each = var.iam_backend_team_roles
    project = var.project
    role    = each.value
    member  = "user:<EMAIL>"
}
resource "google_project_iam_member" "be_rw" {
    for_each = var.iam_backend_team_roles
    project = var.project
    role    = each.value
    member  = "user:<EMAIL>"
}
resource "google_project_iam_member" "be_tk" {
    for_each = var.iam_backend_team_roles
    project = var.project
    role    = each.value
    member  = "user:<EMAIL>"
}

# ╔════════════════════════════════════════════════════════════════════════════╗
# ║ Operations and Infrastrucgture Developers                                  ║
# ╚════════════════════════════════════════════════════════════════════════════╝

resource "google_project_iam_member" "ops_mt" {
    for_each = var.iam_operations_team_roles
    project = var.project
    role    = each.value
    member  = "user:<EMAIL>"
}

# ╔════════════════════════════════════════════════════════════════════════════╗
# ║ Frontend Developers                                                        ║
# ╚════════════════════════════════════════════════════════════════════════════╝

// TODO: split frontend web and frontend mobile roles out and assign CustomRoleMobileFrontend and CustomRoleWebFrontend separately

resource "google_project_iam_member" "fe_rt" {
    for_each = var.iam_frontend_team_roles
    project = var.project
    role    = each.value
    member  = "user:<EMAIL>"
}
resource "google_project_iam_member" "fe_bs" {
    for_each = var.iam_frontend_team_roles
    project = var.project
    role    = each.value
    member  = "user:<EMAIL>"
}
resource "google_project_iam_member" "fe_sc" {
    for_each = var.iam_frontend_team_roles
    project = var.project
    role    = each.value
    member  = "user:<EMAIL>"
}

resource "google_project_iam_member" "fe_aw" {
    for_each = var.iam_frontend_team_roles
    project = var.project
    role    = each.value
    member  = "user:<EMAIL>"
}

resource "google_project_iam_member" "fe_yl" {
    for_each = var.iam_frontend_team_roles
    project = var.project
    role    = each.value
    member  = "user:<EMAIL>"
}

# TODO: Implement Identity Pool Membership for Repos
# From TF: https://github.com/stagedates/infrastructure/blob/7263efb0344cb3574c79106c6a1806003ff05a5e/per_environment/modules/workload_identity_federation/main.tf#L29


# WIP:
# ╔════════════════════════════════════════════════════════════════════════════╗
# ║ Automation WIP                                                             ║
# ╚════════════════════════════════════════════════════════════════════════════╝

# resource "google_project_iam_member" "wif_gh_automation" {
#     project = var.project
#     role    = "roles/iam.workloadIdentityUser"
#     member  = "principalSet://iam.googleapis.com/projects/216250552832/locations/global/workloadIdentityPools/stdts-pool-prod/attribute.repository/stagedates/cloud-functions"
# }
