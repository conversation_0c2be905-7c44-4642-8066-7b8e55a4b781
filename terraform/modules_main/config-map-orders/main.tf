// ╔════════════════════════════════════════════════════════════════════════════════╗
// ║ Version:                                                                   0.1 ║
// ║ Date of Version:                                                    19.09.2024 ║
// ║ Owner:                                                                      SD ║
// ║ Classification:                                                       Internal ║
// ║ Distribution:                                                        All Staff ║
// ╚════════════════════════════════════════════════════════════════════════════════╝

locals {
    name               = var.name
    data               = var.data

    project            = var.project
}

resource "kubernetes_config_map" "main" {
  metadata {
    name      =  "${local.name}-config"
    namespace = local.project
  }

  data = local.data
}

resource "kubernetes_config_map" "entrypoint" {
  metadata {
    name      = "${local.name}-entrypoint"
    namespace = local.project
  }

  data = {
    "entrypoint.sh" = <<EOF
#!/usr/bin/env bash
set -euo pipefail

secret_names_global="PHX_HOST
PHX_PORT
PHX_SERVER
ADYEN_BALANCE_API_KEY
ADYEN_LEM_API_KEY
ADYEN_LIVE_URL_PREFIX
ADYEN_PAYMENTS_API_KEY
ADYEN_WEBHOOK_BAL_HMAC_KEY
ADYEN_WEBHOOK_HMAC_KEY
ADYEN_WEBHOOK_PASSWORD
APPLE_PAY_CERTIFICATE
MERCHANT_ACCOUNT
SEATSIO_ADMIN_KEY
SEATSIO_PRIVATE_DEFAULT_WORKSPACE_KEY
SEATSIO_PUBLIC_DEFAULT_WORKSPACE_KEY
SENDGRID_API_KEY
STAGEDATES_BACKEND_URL
"

secret_names="ADYEN_INITIATIVE_CONTEXT_ORDERS
BACKEND_API_TOKEN_ORDERS
EX_FIREBASE_ISSUER_ORDERS
POSTGRES_DB_ORDERS
POSTGRES_HOST_ORDERS
POSTGRES_PASSWORD_ORDERS
POSTGRES_PORT_ORDERS
POSTGRES_USER_ORDERS
SECRET_KEY_BASE_ORDERS
SERVICE_ACCOUNT_ORDERS
TICKET_SECRET_ORDERS
TICKET_TOPIC_ORDERS
ADYEN_MERCHANT_DISPLAY_NAME_ORDERS
ADYEN_MERCHANT_IDENTIFIER_ORDERS
REDIS_PASSWORD_ORDERS
CASDOOR_APPLICATION_ID_ORDERS
CASDOOR_APPLICATION_SECRET_ORDERS
CASDOOR_CERTIFICATE_ORDERS
CASDOOR_USERNAME_ORDERS
CASDOOR_PASSWORD_ORDERS
UNLEASH_AUTH_TOKEN_ORDERS
"

SECRET_PATH_ROOT=/secrets
AUTH_TOKEN=$(gcloud auth print-access-token)
PROJECT_ID=$(gcloud config get-value project 2> /dev/null)

for secret_name_global in $secret_names_global; do
  echo "Fetching global secret $secret_name_global"
  secret_path=$SECRET_PATH_ROOT/$secret_name_global
  mkdir -p $secret_path
  curl -s "https://secretmanager.googleapis.com/v1/projects/$PROJECT_ID/secrets/$secret_name_global/versions/latest:access" \
    --request "GET" \
    --header "authorization: Bearer $AUTH_TOKEN" \
    --header "content-type: application/json" \
    --header "x-goog-user-project: $PROJECT_ID" | grep '"data":' | head -1 | sed 's/.*"data": *"\([^"]*\)".*/\1/' | base64 -d > $secret_path/secret-file
done

for secret_name in $secret_names; do
  echo "Fetching secret $secret_name"
  secret_name_short=$${secret_name::-7}
  secret_path=$SECRET_PATH_ROOT/$secret_name_short
  mkdir -p $secret_path
  curl -s "https://secretmanager.googleapis.com/v1/projects/$PROJECT_ID/secrets/$secret_name/versions/latest:access" \
    --request "GET" \
    --header "authorization: Bearer $AUTH_TOKEN" \
    --header "content-type: application/json" \
    --header "x-goog-user-project: $PROJECT_ID" | grep '"data":' | head -1 | sed 's/.*"data": *"\([^"]*\)".*/\1/' | base64 -d > $secret_path/secret-file
done
EOF
  }
}
