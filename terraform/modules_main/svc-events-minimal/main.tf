// ╔════════════════════════════════════════════════════════════════════════════════╗
// ║ Version:                                                                    11 ║
// ║ Date of Version:                                                    18.06.2025 ║
// ║ Owner:                                                                      SD ║
// ║ Classification:                                                       Internal ║
// ║ Distribution:                                                        All Staff ║
// ╚════════════════════════════════════════════════════════════════════════════════╝

locals {
  name                      = var.name
  container_port            = 4000
  container_image           = var.container_image
  cloud_run_service_account = var.cloud_run_service_account
  ci_service_account        = var.ci_service_account
  service_suffix_uppercase  = var.service_suffix_uppercase
  service_suffix            = var.service_suffix

  github_owner            = var.github_owner
  github_organization_uri = var.github_organization_uri

  db_cloudsql_instances      = var.db_cloudsql_instances
  vpc_connector              = var.vpc_connector
  project                    = var.project
  region                     = var.region
  platform_version           = var.platform_version
  managed_by_terraform_label = var.managed_by_terraform_label

  environment_variables = {
    "MAIL_PUBSUB_TOPIC"                      = var.pubsub_email_topic_name,
    "PDF_PUBSUB_TOPIC"                       = var.pubsub_email_subscription_name,
    "SERVICE_NAME"                           = var.name
    "LOG_LEVEL"                              = var.default_log_level
    "STAGE_DATE_BACKEND"                     = var.backend_hostname,
    "GCLOUD_PROJECT"                         = var.project
    "ENVIRONMENT"                            = var.environment
    "POSTGRES_SCHEMA"                        = var.postgres_schema
    "TICKET_SUBSCRIPTION_NAME"               = "events-worker.orders.tickets"
    "SALES_CHANNELS_PUBSUB_TOPIC"            = "events.sales_channels",
    "FUTURE_DEMAND_EVENTS_SUBSCRIPTION_NAME" = "events-worker.future-demand.events"
    "TICKET_CATEGORIES_PUBSUB_TOPIC"         = var.pubsub_ticket_categories_topic_name
    "FRONTEND_URL"                           = var.frontend_url_events,
    "BACKEND_URL"                            = var.backend_url_events,
    "SHORTLINK_URL"                          = var.shortlink_url,
    "CASDOOR_URL"                            = var.casdoor_url_events,
    "CASDOOR_ORGANIZATION_NAME"              = var.casdoor_organization_name_events,
    "CASDOOR_PROMOTER_DOMAIN_NAME"           = var.casdoor_promoter_domain_name_events,
    "CASDOOR_USER_MODEL_NAME"                = var.casdoor_user_model_name_events,
    "CASDOOR_USER_ENFORCER_NAME"             = var.casdoor_user_enforcer_name_events,
    "IMPORTED_TICKETS_PUBSUB_TOPIC"          = var.pubsub_imported_tickets_topic_name,
    "IMPORTED_TICKETS_PUBSUB_SUBSCRIPTION"   = var.pubsub_imported_tickets_subscription_name,
    "GKE_URL"                                = var.gke_url_events
  }

  // suffixed:
  local_secrets_to_declare = [
  ]

  // suffixed:
  local_secrets_to_mount = [ // ng
    "POSTGRES_DB",
    "POSTGRES_HOST",
    "POSTGRES_PASSWORD",
    "POSTGRES_PORT",
    "POSTGRES_USER",
    "SERVICE_CLIENT_ID",
    "SERVICE_CLIENT_SECRET",
    "BACKEND_API_TOKEN", // TODO: FixMe: declared at other modules.
    "SECRET_KEY_BASE",
    "EX_FIREBASE_ISSUER",
    "TRACKING_LINK_URL",
    "TRACKING_LINK_API_KEY",
    "CDN_URL",
    "SERVICE_ACCOUNT",
    "TICKET_SECRET",
    "SCHEDULER_TOKEN",
    "TICKET_SUBSCRIPTION_NAME",
    "ADYEN_WEBHOOK_BALANCE_PLATFORM_USERNAME",
    "ADYEN_WEBHOOK_BALANCE_PLATFORM_PASSWORD",
    "UNLEASH_URL",
    "UNLEASH_APP_NAME",
    "UNLEASH_INSTANCE_ID",
    "UNLEASH_AUTH_TOKEN",
    "GOOGLE_GEOCODING_API_KEY",
    "CASDOOR_CERTIFICATE",
    "CASDOOR_APPLICATION_ID",
    "CASDOOR_APPLICATION_SECRET",
    "CASDOOR_USERNAME",
    "CASDOOR_PASSWORD"
  ]

  // unsuffixed
  global_secrets_to_mount = [ // ng
    "ADYEN_ENV",
    "PHX_HOST",
    "PHX_PORT",
    "PHX_SERVER",
    "ADYEN_LEM_API_KEY",
    "ADYEN_BALANCE_API_KEY",
    "ADYEN_PAYMENTS_API_KEY",
    "ADYEN_WEBHOOK_BAL_HMAC_KEY",
    "ADYEN_LIVE_URL_PREFIX",
    "MERCHANT_ACCOUNT",
    //"SCHEDULER_TOKEN",
    "SEATSIO_ADMIN_KEY",
    "SEATSIO_PRIVATE_DEFAULT_WORKSPACE_KEY",
    "SEATSIO_PUBLIC_DEFAULT_WORKSPACE_KEY",
    "SPOTIFY_CLIENT_ID",
    "SPOTIFY_SECRET",
    "STAGEDATES_BACKEND_URL"
  ]

  // unsuffixed
  global_secrets_to_declare = [
  ]

  // Performance settings:
  min_scaling_instance_count = var.min_scaling_instance_count
  max_scaling_instance_count = var.max_scaling_instance_count
  resources_cpu_idle         = var.resources_cpu_idle
  startup_cpu_boost          = var.startup_cpu_boost
  resources_limits_cpu       = var.resources_limits_cpu
  resources_limits_memory    = var.resources_limits_memory
}

module "events-svc-cr" {
  service_name             = local.name
  service_suffix           = local.service_suffix
  service_suffix_uppercase = local.service_suffix_uppercase

  local_secrets_to_declare  = local.local_secrets_to_declare
  local_secrets_to_mount    = local.local_secrets_to_mount
  global_secrets_to_declare = local.global_secrets_to_declare
  global_secrets_to_mount   = local.global_secrets_to_mount

  local_environment_variables   = local.environment_variables
  service_environment_variables = local.environment_variables

  container_port   = local.container_port
  container_image  = local.container_image
  platform_version = local.platform_version

  managed_by_terraform_label    = local.managed_by_terraform_label
  db_cloudsql_instances         = local.db_cloudsql_instances
  cloud_run_service_account     = local.cloud_run_service_account
  vpc_connector                 = local.vpc_connector
  global_shared_secrets         = local.global_secrets_to_mount
  global_shared_secrets_phoenix = local.global_secrets_to_mount
  secrets_database_connection   = local.local_secrets_to_mount

  project = local.project
  region  = local.region


  // Performance settings:
  min_scaling_instance_count = local.min_scaling_instance_count
  max_scaling_instance_count = local.max_scaling_instance_count
  resources_cpu_idle         = local.resources_cpu_idle
  startup_cpu_boost          = local.startup_cpu_boost
  resources_limits_cpu       = local.resources_limits_cpu
  resources_limits_memory    = local.resources_limits_memory

  source = "../../modules/cloudrun-v03"

  depends_on = [
  ]
}
