// ╔════════════════════════════════════════════════════════════════════════════════╗
// ║ Version:                                                                    04 ║
// ║ Date of Version:                                                    18.06.2024 ║
// ║ Owner:                                                                      SD ║
// ║ Classification:                                                       Internal ║
// ║ Distribution:                                                        All Staff ║
// ╚════════════════════════════════════════════════════════════════════════════════╝

variable "environment" {
  type        = string
  default     = "prod"
  description = "This reflects the environment, so that services like seats.io are able to behave differently. Possible values: dev, prod, local [SI-146]"
}
variable "default_log_level" {
  type        = string
  default     = "info"
  description = "The default log level for the service"
}
variable "postgres_schema" {
  type        = string
  default     = "events"
  description = "The postgres schema the service works with."
}
variable "backend_hostname" { type = string }
variable "secret_key_base" { type = string }

variable "pubsub_email_topic_name" { type = string }
variable "pubsub_email_subscription_name" { type = string }

//variable "global_secrets_to_declare" { type = list(string) }
//variable "global_secrets_to_mount" { type = list(string) }
//variable "local_secrets_to_declare" { type = list(string) }
//variable "local_secrets_to_mount" { type = list(string) }

variable "vpc_connector" { type = string }
variable "db_cloudsql_instances" { type = string }

variable "service_suffix_uppercase" { type = string }
variable "service_suffix" { type = string }
variable "gke_url_events" { type = string }

variable "container_image" { type = string }

variable "github_owner" { type = string }
variable "github_organization_uri" { type = string }

variable "name" { type = string }
variable "platform_version" { type = string }

variable "cloud_run_service_account" {
  type        = string
  description = "The service account to run the resource - Needed for consuming internal cloud resources and services"
}
variable "ci_service_account" {
  type        = string
  description = "The CI service account to build with - Needed for Cloud Build triggers"
}

variable "project" { type = string }

variable "region" {
  type        = string
  default     = "europe-west3"
  description = "The region to base the resource in"
}

variable "managed_by_terraform_label" {
  type        = string
  default     = "terraform"
  description = "A label for the resource description field"
}

variable "pubsub_tickets_topic_name" {
  type        = string
  default     = "orders.tickets"
  description = "Name of the tickets PubSub topic"
}

variable "pubsub_ticket_categories_topic_name" {
  type        = string
  description = "Name of the ticket categories PubSub topic"
}

variable "pubsub_tickets_subscription_name" {
  type        = string
  default     = "orders.tickets-sub"
  description = "Name of the tickets PubSub subscription"
}

variable "frontend_url_events" {
  type        = string
  description = "The frontend URL for the events service"
}

variable "backend_url_events" {
  type        = string
  description = "The backend URL for the events service"
}

variable "shortlink_url" {
  type        = string
  description = "The shortlink URL for the events service"
}

variable "casdoor_url_events" {
  type        = string
  description = "The casdoor URL for the events service"

}

variable "casdoor_organization_name_events" {
  type        = string
  description = "The casdoor organization name for the events service"
}

variable "casdoor_promoter_domain_name_events" {
  type        = string
  description = "The casdoor promoter domain name for the events service"
}

variable "casdoor_user_model_name_events" {
  type        = string
  description = "The casdoor user model name for the events service"
}

variable "casdoor_user_enforcer_name_events" {
  type        = string
  description = "The casdoor user enforcer name for the events service"
}


variable "pubsub_imported_tickets_topic_name" {
  type        = string
  description = "Name of the events PubSub topic for imported tickets"
}

variable "pubsub_imported_tickets_subscription_name" {
  type        = string
  description = "Name of the events PubSub subscription for imported tickets"
}


// Performance^

// scaling_min_instance_count = 1
variable "min_scaling_instance_count" {
  type        = string
  default     = "1"
  description = "Minimum instance count"
}

// scaling_max_instance_count = 3
variable "max_scaling_instance_count" {
  type        = string
  default     = "3"
  description = "Maximum instance number"
}

variable "startup_cpu_boost" {
  type        = bool
  default     = false
  description = "Enable idle cpu booster - helps reducing cold-start latency"
}

variable "resources_cpu_idle" {
  type        = bool
  default     = false
  description = "Enable allow cpu idle - beware cost factor"
}

// resources_limits_cpu = "2000m"
variable "resources_limits_cpu" {
  type        = string
  default     = "2000m"
  description = "CPU limits in Milli CPUs"
}


// resources_limits_memory = "4096Mi"
variable "resources_limits_memory" {
  type        = string
  default     = "4096Mi"
  description = "Memory limit in MiBi Bytes"
}

// Performance$
