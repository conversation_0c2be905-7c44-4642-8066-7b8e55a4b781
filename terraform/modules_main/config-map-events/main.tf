// ╔════════════════════════════════════════════════════════════════════════════════╗
// ║ Version:                                                                    02 ║
// ║ Date of Version:                                                    24.01.2025 ║
// ║ Owner:                                                                      SD ║
// ║ Classification:                                                       Internal ║
// ║ Distribution:                                                        All Staff ║
// ╚════════════════════════════════════════════════════════════════════════════════╝

locals {
    name               = var.name
    data               = var.data

    project            = var.project
}

resource "kubernetes_config_map" "main" {
  metadata {
    name      =  "${local.name}-config"
    namespace = local.project
  }

  data = local.data
}

resource "kubernetes_config_map" "entrypoint" {
  metadata {
    name      = "${local.name}-entrypoint"
    namespace = local.project
  }

  data = {
    "entrypoint.sh" = <<EOF
#!/usr/bin/env bash
set -euo pipefail

secret_names_global="ADYEN_ENV
PHX_HOST
PHX_PORT
PHX_SERVER
ADYEN_LEM_API_KEY
ADYEN_BALANCE_API_KEY
ADYEN_PAYMENTS_API_KEY
ADYEN_WEBHOOK_BAL_HMAC_KEY
ADYEN_LIVE_URL_PREFIX
MERCHANT_ACCOUNT
SEATSIO_ADMIN_KEY
SEATSIO_PRIVATE_DEFAULT_WORKSPACE_KEY
SEATSIO_PUBLIC_DEFAULT_WORKSPACE_KEY
SPOTIFY_CLIENT_ID
SPOTIFY_SECRET
STAGEDATES_BACKEND_URL
"

secret_names="POSTGRES_DB_EVENTS
POSTGRES_HOST_EVENTS
POSTGRES_PASSWORD_EVENTS
POSTGRES_PORT_EVENTS
POSTGRES_USER_EVENTS
SERVICE_CLIENT_ID_EVENTS
SERVICE_CLIENT_SECRET_EVENTS
BACKEND_API_TOKEN_EVENTS
SECRET_KEY_BASE_EVENTS
EX_FIREBASE_ISSUER_EVENTS
TRACKING_LINK_URL_EVENTS
TRACKING_LINK_API_KEY_EVENTS
CDN_URL_EVENTS
SERVICE_ACCOUNT_EVENTS
TICKET_SECRET_EVENTS
SCHEDULER_TOKEN_EVENTS
TICKET_SUBSCRIPTION_NAME_EVENTS
ADYEN_WEBHOOK_BALANCE_PLATFORM_USERNAME_EVENTS
ADYEN_WEBHOOK_BALANCE_PLATFORM_PASSWORD_EVENTS
UNLEASH_URL_EVENTS
UNLEASH_APP_NAME_EVENTS
UNLEASH_INSTANCE_ID_EVENTS
UNLEASH_AUTH_TOKEN_EVENTS
GOOGLE_GEOCODING_API_KEY_EVENTS
CASDOOR_CERTIFICATE_EVENTS
CASDOOR_APPLICATION_ID_EVENTS
CASDOOR_APPLICATION_SECRET_EVENTS
CASDOOR_USERNAME_EVENTS
CASDOOR_PASSWORD_EVENTS
"

SECRET_PATH_ROOT=/secrets
AUTH_TOKEN=$(gcloud auth print-access-token)
PROJECT_ID=$(gcloud config get-value project 2> /dev/null)

for secret_name_global in $secret_names_global; do
  echo "Fetching global secret $secret_name_global"
  secret_path=$SECRET_PATH_ROOT/$secret_name_global
  mkdir -p $secret_path
  curl -s "https://secretmanager.googleapis.com/v1/projects/$PROJECT_ID/secrets/$secret_name_global/versions/latest:access" \
    --request "GET" \
    --header "authorization: Bearer $AUTH_TOKEN" \
    --header "content-type: application/json" \
    --header "x-goog-user-project: $PROJECT_ID" | grep '"data":' | head -1 | sed 's/.*"data": *"\([^"]*\)".*/\1/' | base64 -d > $secret_path/secret-file
done

for secret_name in $secret_names; do
  echo "Fetching secret $secret_name"
  secret_name_short=$${secret_name::-7}
  secret_path=$SECRET_PATH_ROOT/$secret_name_short
  mkdir -p $secret_path
  curl -s "https://secretmanager.googleapis.com/v1/projects/$PROJECT_ID/secrets/$secret_name/versions/latest:access" \
    --request "GET" \
    --header "authorization: Bearer $AUTH_TOKEN" \
    --header "content-type: application/json" \
    --header "x-goog-user-project: $PROJECT_ID" | grep '"data":' | head -1 | sed 's/.*"data": *"\([^"]*\)".*/\1/' | base64 -d > $secret_path/secret-file
done
EOF
  }
}
