// ╔════════════════════════════════════════════════════════════════════════════════╗
// ║ Version:                                                                    05 ║
// ║ Date of Version:                                                    27.06.2025 ║
// ║ Owner:                                                                      SD ║
// ║ Classification:                                                       Internal ║
// ║ Distribution:                                                        All Staff ║
// ╚════════════════════════════════════════════════════════════════════════════════╝

//      _                _   _ _       _    
//  ___| |__   ___  _ __| |_| (_)_ __ | | __
// / __| '_ \ / _ \| '__| __| | | '_ \| |/ /
// \__ \ | | | (_) | |  | |_| | | | | |   < 
// |___/_| |_|\___/|_|   \__|_|_|_| |_|_|\_\

// provisions the cluster resources for the service
module "k8s-a01-service-shortlink" {
  service_name         = "shortlink"
  nodepool_label       = "backend"
  cluster_name         = local.gke-main.cluster_name
  service_type         = "ClusterIP"
  image                = "europe-west3-docker.pkg.dev/${var.project}/shortlink-service/shortlink-service:latest"
  label_elixir_cluster = "backend-api"

  hpa_max_replicas = 2
  hpa_min_replicas = 1

  service_port        = "8080"
  service_port_name   = "tcp-shortlink"
  service_target_port = "8080"

  readiness_path            = "/healthz"
  readiness_port            = "8080"
  readiness_initial_delay   = "20"
  readiness_period_seconds  = "10"
  readiness_timeout_seconds = "10"

  liveness_path                  = "/healthz"
  liveness_port                  = "8080"
  liveness_initial_delay_seconds = "20"
  liveness_period_seconds        = "10"
  liveness_timeout_seconds       = "10"

  resources_limits_cpu      = "1.0"
  resources_limits_memory   = "2048Mi"
  resources_requests_cpu    = "250m"
  resources_requests_memory = "300Mi"

  project          = var.project
  region           = local.gke-main.region
  platform_version = local.gke-main.platform_version

  source = "./modules/gke_service_hpa"

  depends_on = [
    //	module.gke-main
  ]
}

module "config-map-shortlink" {
  name = "shortlink"
  data = {
    description                   = "shortlink's environment variables",
    APP_NAME                      = "shortlink",
    STAGE_DATE_BACKEND            = var.host_name_main,
    FRONTEND_URL                  = "https://${var.host_name_main}",
    GCLOUD_PROJECT                = local.gke-main.project,
    SECRET_KEY_BASE               = var.secret_key_base_global,
    OTEL_EXPORTER_OTLP_ENDPOINT   = var.otel_exporter_otlp_endpoint_http,
    HTTP_PORT                     = "8080",
    GOFR_ENABLED                  = "true",
    LOG_LEVEL                     = "DEBUG",
    TRACE_EXPORTER                = "otlp",
    TRACER_URL                    = var.otel_exporter_otlp_endpoint_otlp,
    DB_HOST                       = "db.internal",
    DB_PORT                       = "5432",
    DB_USER                       = "shortlinksvc",
    DB_PASSWORD                   = var.db_password_shortlink, # TODO: this is a secret. Implement a shortlink.getSecretFromFilesystem() and move this to secret manager
    DB_NAME                       = "backend",
    DB_SSL_MODE                   = "disable",
    DB_DIALECT                    = "postgres",
    DB_MAX_LIFETIME_CONNECTION    = 5, # in minutes
    DB_MAX_IDLE_CONNECTION        = 5,
    DB_MAX_OPEN_CONNECTION        = 10,
    REDIS_ENDPOINT_HOST_AND_PORT  = "redis-master.${local.gke-main.project}.svc.cluster.local:6379",
    REDIS_PASSWORD                = var.redis_password_shortlink,
    EVENTS_ENDPOINT_HOST_AND_PORT = "http://events-gke.${local.gke-main.project}.svc.cluster.local:8080",
  }

  label_managed_by_terraform = "terraform"
  platform_version           = local.gke-main.platform_version

  project = local.gke-main.project
  source  = "./modules_main/config-map-shortlink"
}

module "shortlink-secrets" {
  secrets               = ["DB_PASSWORD", "REDIS_PASSWORD"]
  global_shared_secrets = []

  service_name             = "shortlink-service"
  platform_version         = var.platform_version_current
  service_suffix           = "shortlink"
  service_suffix_uppercase = "SHORTLINK"

  project = var.project
  region  = var.region

  managed_by_terraform_label = "terraform"

  source = "./modules/secrets"
}

module "shortlink-pub-sub" {
  topic_name                 = "events.events"
  subscription_name          = "shortlink-worker.events.events"
  message_retention_duration = var.message_retention_duration

  project                    = var.project
  region                     = var.region
  managed_by_terraform_label = var.managed_by_terraform_label_short_lowercase
  platform_version           = var.platform_version_v24Q4

  source = "./modules/pubsub-subscription"
}

module "ci-triggers-shortlink" {
  service_name = "shortlink"

  service_ci_service_account       = local.service_ci_service_account
  github_owner                     = local.github_owner
  github_organization_uri          = local.github_organization_uri
  service_ci_trigger_branch        = local.service_ci_trigger_branch
  managed_by_terraform_label_short = local.managed_by_terraform_label

  project = local.project
  region  = local.region

  source = "./modules/ci_triggers"
}

module "shortlink-artifact-registry" {
  name = "shortlink-service"

  project                    = var.project
  region                     = var.region
  managed_by_terraform_label = var.managed_by_terraform_label_short_lowercase
  platform_version           = var.platform_version_current

  source = "./modules/gcp_artifact_registry"
}

