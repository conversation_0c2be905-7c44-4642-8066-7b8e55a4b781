# ╔════════════════════════════════════════════════════════════════════════════════╗
# ║ Version:                                                                    09 ║
# ║ Date of Version:                                                    19.03.2025 ║
# ║ Owner:                                                                      SD ║
# ║ Classification:                                                       Internal ║
# ║ Distribution:                                                        All Staff ║
# ╚════════════════════════════════════════════════════════════════════════════════╝

# HOWTO: First of all source your .env file.
# HOWTO: For more details please refer to the README.md in this folder.


SHELL := /bin/bash

.PHONY: help
help:
	@echo USAGE:
	@echo
	@echo "1. init the environment:"
	@echo -e "\tsource .env.<ENV>"
	@echo -e "\t./init.sh --<ENV>"
	@echo
	@echo "2. Static service tasks to re-provision a backend service infra components:"
	@echo -e "\tmake svc-accounts"
	@echo -e "\tmake svc-carts"
	@echo -e "\tmake svc-emails"
	@echo -e "\tmake svc-events"
	@echo -e "\tmake svc-fulfillment"
	@echo -e "\tmake svc-future-demands"
	@echo -e "\tmake svc-orders"
	@echo -e "\tmake svc-pdf"
	@echo -e "\tmake svc-reports"
	@echo
	@echo
	@echo "2. Dynamic tasks:"
	@echo -e "\t< ACTION=<action> COMPONENT=<component> SVC=<svc> make magic [-n] | make graph >"
	@echo
	@echo
	@echo -e "WHERE:"
	@echo -e "\taction:\t\tapply | import | destroy"
	@echo -e "\tcomponent:\tcloudrun | triggers | secrets | pubsub | datastream | secretmanager"
	@echo -e "\tsvc:\t\torders | events | com | accounts | main | globals"
	@echo
	@echo -e "\tgraph:\t\tgenerates a terraform state graph according to https://developer.hashicorp.com/terraform/cli/commands/graph"
	@echo
	@echo -e "EXAMPLE:"
	@echo -e "\tACTION=apply COMPONENT=cloudrun SVC=emails make magic [-n]\n"

ENVIRONMENT = ${shell echo ${INTEGRATION_ENVIRONMENT}}
tgt=-target
m=-target module.

define tf_apply
	terragrunt apply --terragrunt-config terragrunt.hcl --terragrunt-working-dir ${SUBPROJECT_FOLDER} -var-file=variables-${ENVIRONMENT}.tfvars -var-file=secrets-${ENVIRONMENT}.tfvars
endef
export tf_apply

define tf_import
	terragrunt import --terragrunt-config terragrunt.hcl --terragrunt-working-dir ${SUBPROJECT_FOLDER} -var-file=variables-${ENVIRONMENT}.tfvars -var-file=secrets-${ENVIRONMENT}.tfvars 
endef
export tf_import

define tf_destroy
	terragrunt destroy --terragrunt-config terragrunt.hcl --terragrunt-working-dir ${SUBPROJECT_FOLDER} -var-file=variables-${ENVIRONMENT}.tfvars -var-file=secrets-${ENVIRONMENT}.tfvars
endef
export tf_destroy

.PHONY: tf-${ACTION}-${COMPONENT}-${SVC}
tf-${ACTION}-${COMPONENT}-${SVC}:
	${tf_apply} ${tgt} module.${COMPONENT}-${SVC}

.PHONY: magic
magic:
	@make tf-${ACTION}-${COMPONENT}-${SVC}

.PHONY: tf-init
tf-init:
	terragrunt init --terragrunt-working-dir ${SUBPROJECT_FOLDER}

.PHONY: tg-login
tf-login:
	terragrunt login

.PHONY: init
init:
ifeq ($(ENV),)
	@echo "Error: ENV variable is not set. Usage: ENV=<environment> make init"
	@exit 1
else
	./init.sh --$(ENV)
endif
#.PHONY: init
# init:
#	./init.sh

.PHONY: graph
graph:
	terraform graph | dot -Tsvg > terraform_foundation_graph.svg

.PHONY: create-networking-nat
create-networking-nat:
	${tf_apply} ${tgt} module.networking-nat

# This is a.o. the ip address of the demo environment
.PHONY: create-gcp-networking
create-gcp-networking:
	${tf_apply} ${tgt} module.gcp-networking

.PHONY: create-k8s-main
create-k8s-main:
	${tf_apply} -target module.gke-main

.PHONY: tf-apply-k8s-istio-subsystem
tf-apply-k8s-istio-subsystem:
	${tf_apply} -target module.gke-istio

.PHONY: tf-apply-k8s-nfs
tf-apply-k8s-nfs:
	${tf_apply} ${tgt} module.gke-nfs

.PHONY: k8s-pool-infra
k8s-pool-infra:
	${tf_apply} ${tgt} module.k8s-a01-nodepool-infra



.PHONY: svc-accounts
svc-accounts:
	${tf_apply} ${m}accounts-svc ${m}config-map-accounts ${m}k8s-a01-service-accounts

.PHONY: svc-carts
svc-carts:
	${tf_apply} ${m}config-map-carts ${m}k8s-a01-service-carts

.PHONY: svc-com
svc-com:
	${tf_apply} ${m}com-cr-minimal ${m}config-map-com ${m}k8s-a01-service-com

.PHONY: svc-emails
svc-emails:
	${tf_apply} ${m}emails-svc ${m}config-map-emails ${m}k8s-a01-service-emails

.PHONY: svc-events
svc-events:
	${tf_apply} ${m}events-cr-minimal ${m}config-map-events ${m}k8s-a01-service-events

.PHONY: svc-fulfillment
svc-fulfillment:
	${tf_apply} ${m}k8s-a01-service-fulfillment ${m}config-map-fulfillment

.PHONY: svc-future-demands
svc-future-demands:
	${tf_apply} ${m}config-map-future-demand ${m}k8s-a01-service-futuredemand

.PHONY: svc-orders
svc-orders:
	${tf_apply} ${m}orders-svc ${m}config-map-orders ${m}k8s-a01-service-orders

.PHONY: svc-pdf
svc-pdf:
	${tf_apply} ${m}config-map-pdf ${m}k8s-a01-service-pdf

.PHONY: svc-reports
svc-reports:
	${tf_apply} ${m}reports-cr ${m}config-map-reports ${m}k8s-a01-service-reports

.PHONY: svc-waiting-rooms
svc-waiting-rooms:
	${tf_apply} ${m}k8s-a01-service-waiting-rooms ${m}config-map-waiting-rooms