// ╔════════════════════════════════════════════════════════════════════════════════╗
// ║ Version:                                                                    07 ║
// ║ Date of Version:                                                    27.06.2025 ║
// ║ Owner:                                                                      SD ║
// ║ Classification:                                                       Internal ║
// ║ Distribution:                                                        All Staff ║
// ╚════════════════════════════════════════════════════════════════════════════════╝

locals {
  service_name         = var.service_name
  cluster_name         = var.cluster_name
  nodepool_label       = var.nodepool_label
  service_type         = var.service_type
  image                = var.image
  label_elixir_cluster = var.label_elixir_cluster
  hpa_max_replicas     = var.hpa_max_replicas
  hpa_min_replicas     = var.hpa_min_replicas

  service_port        = var.service_port
  service_port_name   = var.service_port_name
  service_target_port = var.service_target_port

  readiness_path            = var.readiness_path
  readiness_port            = var.readiness_port
  readiness_period_seconds  = var.readiness_period_seconds
  readiness_initial_delay   = var.readiness_initial_delay
  readiness_timeout_seconds = var.readiness_timeout_seconds

  liveness_path                  = var.liveness_path
  liveness_port                  = var.liveness_port
  liveness_initial_delay_seconds = var.liveness_initial_delay_seconds
  liveness_period_seconds        = var.liveness_period_seconds
  liveness_timeout_seconds       = var.liveness_timeout_seconds

  resources_limits_cpu      = var.resources_limits_cpu
  resources_limits_memory   = var.resources_limits_memory
  resources_requests_cpu    = var.resources_requests_cpu
  resources_requests_memory = var.resources_requests_memory

  project          = var.project
  region           = var.region
  platform_version = var.platform_version
}

resource "kubernetes_service_v1" "main" {
  metadata {
    name      = "${local.service_name}-gke"
    namespace = local.project
    labels = {
      app = local.service_name
    }

    annotations = {
      "cloud.google.com/neg" = "{\"ingress\": true}"
      //    "cloud.google.com/backend-config" = "{\"ports\": {\"80\":\"stagedates-<SERVICE_NAME>-v1\"}}"
    }
  }
  spec {
    selector = {
      app = local.service_name // this is kubernetes_pod.example.metadata.0.labels.app
    }
    // Architecture Decision: no session affinity for now
    // session_affinity = "ClientIP"

    port {
      port        = local.service_port
      target_port = local.service_target_port
      name        = local.service_port_name
    }

    type = local.service_type
  }
}


resource "kubernetes_horizontal_pod_autoscaler_v1" "main" {
  metadata {
    name      = local.service_name
    namespace = local.project
  }

  spec {
    max_replicas = 1 // local.hpa_max_replicas
    min_replicas = 1 // local.hpa_min_replicas

    scale_target_ref {
      name        = local.service_name
      api_version = "apps/v1"
      kind        = "Deployment"
    }

  }
}

resource "kubernetes_deployment_v1" "main" {
  metadata {
    name      = local.service_name
    namespace = local.project
    labels = {
      app            = local.service_name
      elixir_cluster = local.label_elixir_cluster
    }

  }

  spec {
    replicas               = 1
    revision_history_limit = 5

    selector {
      match_labels = {
        app = local.service_name
      }

    }

    strategy {
      type = "RollingUpdate"
      rolling_update {
        max_surge       = 1
        max_unavailable = 0
      }
    }

    template {
      metadata {
        labels = {
          app            = local.service_name
          elixir_cluster = local.label_elixir_cluster
          version        = local.platform_version
        }
      }

      spec {
        dns_policy           = "ClusterFirst"
        service_account_name = "cluster"

        volume {
          name = "${local.service_name}-entrypoint"

          config_map {
            name         = "${local.service_name}-entrypoint"
            default_mode = "0744"
          }
        }

        volume {
          name = "secret-volume"

          empty_dir {}
        }

        // ---

        init_container {
          image = "gcr.io/google.com/cloudsdktool/cloud-sdk:slim"
          name  = "init"

          command = ["/scripts/entrypoint.sh"]

          volume_mount {
            mount_path = "/scripts"
            name       = "${local.service_name}-entrypoint"
            read_only  = false
          }

          volume_mount {
            mount_path = "/secrets"
            name       = "secret-volume"
            read_only  = false
          }

          # env {
          #   name  = "SECRET_PATH_ROOT"
          #   value = "/secrets"
          # }
        }

        // main container
        container {

          image             = local.image
          image_pull_policy = "Always" // "IfNotPresent" // "Always"
          name              = local.service_name

          // TODO
          env_from {
            config_map_ref {
              name = "${local.service_name}-config"
              // name = kubernetes_config_map.main.metadata.0.name
            }
          }
          env_from {
            config_map_ref {
              name = "global-config"
            }
          }

          volume_mount {
            mount_path = "/secrets"
            name       = "secret-volume"
            read_only  = true
          }

          resources {
            // TODO: observe and optimize limits{}
            limits = {
              cpu    = local.resources_limits_cpu
              memory = local.resources_limits_memory
            }
            // TODO: observe and optimize requests{}
            requests = {
              cpu    = local.resources_requests_cpu
              memory = local.resources_requests_memory
            }
          }

          port {
            container_port = local.service_target_port
          }

          readiness_probe {
            http_get {
              path = local.readiness_path
              port = local.readiness_port
            }
            initial_delay_seconds = local.readiness_initial_delay
            period_seconds        = local.readiness_period_seconds
            timeout_seconds       = local.readiness_timeout_seconds
          }

          liveness_probe {
            http_get {
              path = local.liveness_path
              port = local.liveness_port

              # http_header {
              #   name  = "X-Custom-Header"
              #   value = "Awesome"
              # }
            }
            initial_delay_seconds = 600 // local.liveness_initial_delay_seconds
            period_seconds        = 600 // local.liveness_period_seconds
            timeout_seconds       = 600 // local.liveness_timeout_seconds
          }
        }
        // main container

        // TODO: Affinity!
        affinity {
          ## Coexistence constraint:
          ## no coexistence: one pod per node
          //pod_anti_affinity { // TODO:
          //  required_during_scheduling_ignored_during_execution {
          //    namespaces   = []
          //    topology_key = "kubernetes.io/hostname"
          //    label_selector {
          //      match_labels = {}
          //      match_expressions {
          //        key      = "app"
          //        operator = "In"
          //        values   = ["${var.svc-name}"]
          //      }
          //    }
          //  }
          //}
          node_affinity {
            required_during_scheduling_ignored_during_execution {
              node_selector_term {
                ## Geografical constraint:
                match_expressions {
                  key      = "topology.kubernetes.io/region"
                  operator = "In"
                  values   = ["${local.region}"]
                }
                ## Server type constraint:
                match_expressions {
                  key      = "component"
                  operator = "In"
                  values   = [local.nodepool_label]
                }
                ## Server type constraint:
                //match_expressions { // TODO:
                //  key      = "component"
                //  operator = "NotIn"
                //  values   = ["monitoring", "monitoring"]
                //}
              }
            }
          }
        }


      }
    }
  }
}
