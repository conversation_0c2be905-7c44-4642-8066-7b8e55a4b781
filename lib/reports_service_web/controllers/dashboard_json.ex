defmodule ReportsServiceWeb.DashboardJSON do
  @moduledoc false

  @days_of_week %{
    "1" => "Monday",
    "2" => "Tuesday",
    "3" => "Wednesday",
    "4" => "Thursday",
    "5" => "Friday",
    "6" => "Saturday",
    "7" => "Sunday"
  }

  def show_ticket_sales(%{ticket_sales: ticket_sales}) do
    for(
      ticket_sales <- ticket_sales,
      do: show_ticket_sales_data(ticket_sales)
    )
  end

  def show_event_ticket_sales(%{ticket_sales: ticket_sales}) do
    for(
      ticket_sales <- ticket_sales,
      do: show_event_ticket_sales_data(ticket_sales)
    )
  end

  def show_total_sales(%{
        total_sales: total_sales,
        total_sales_last_day: total_sales_last_day,
        total_sales_last_week: total_sales_last_week,
        total_sales_last_month: total_sales_last_month
      }) do
    %{
      total_sales: show_total_sales_data(total_sales),
      total_sales_last_day: show_total_sales_data(total_sales_last_day),
      total_sales_last_week: show_total_sales_data(total_sales_last_week),
      total_sales_last_month: show_total_sales_data(total_sales_last_month)
    }
  end

  def show_sold_tickets_for_event(%{ticket_sales: ticket_sales, display_as: "relative"}) do
    for ticket_sales <- ticket_sales do
      ticket_sales =
        case ticket_sales.quota do
          nil ->
            ticket_sales
            |> Map.put(:sold_tickets, 0)
            |> Map.put(:sold_extras, 0)
            |> Map.put(:sold_products, 0)
            |> Map.put(:quota, 0)

          0 ->
            ticket_sales
            |> Map.put(:sold_tickets, 0)
            |> Map.put(:sold_extras, 0)
            |> Map.put(:sold_products, 0)
            |> Map.put(:quota, 0)

          _quota ->
            ticket_sales
            |> Map.put(:sold_tickets, round(ticket_sales.sold_tickets / ticket_sales.quota * 100))
            |> Map.put(:sold_extras, round(ticket_sales.sold_extras / ticket_sales.quota * 100))
            |> Map.put(
              :sold_products,
              round(ticket_sales.sold_products / ticket_sales.quota * 100)
            )
            |> Map.put(:quota, 100)
        end

      show_sold_tickets_for_event_data(ticket_sales)
    end
  end

  def show_sold_tickets_for_event(%{ticket_sales: ticket_sales, display_as: _display_as}) do
    for(
      ticket_sales <- ticket_sales,
      do: show_sold_tickets_for_event_data(ticket_sales)
    )
  end

  def count_sold_tickets(%{sold_tickets: sold_tickets, group_by: group_by}) do
    count_sold_tickets =
      for(sold_tickets <- sold_tickets) do
        count_sold_tickets_data(sold_tickets, group_by)
      end

    sum_sold_tickets =
      sold_tickets
      |> Enum.map(fn item -> item.count_tickets end)
      |> Enum.sum()

    case Enum.count(count_sold_tickets) do
      0 ->
        %{sold_tickets: count_sold_tickets, avg: 0}

      count_value ->
        %{sold_tickets: count_sold_tickets, avg: sum_sold_tickets / count_value}
    end
  end

  defp count_sold_tickets_data(sold_tickets, "day") do
    %{
      day: sold_tickets.period,
      sold: sold_tickets.count_tickets
    }
  end

  defp count_sold_tickets_data(sold_tickets, "hour") do
    %{
      hour: sold_tickets.period,
      sold: sold_tickets.count_tickets
    }
  end

  defp count_sold_tickets_data(sold_tickets, "day_hour") do
    %{
      day: Map.get(@days_of_week, Decimal.to_string(sold_tickets.day)),
      hour: sold_tickets.hour,
      sold: sold_tickets.count_tickets
    }
  end

  defp count_sold_tickets_data(sold_tickets, _period) do
    %{
      period: sold_tickets.period,
      sold: sold_tickets.count_tickets
    }
  end

  defp show_ticket_sales_data(ticket_sales) do
    %{
      date: ticket_sales.date,
      event_id: ticket_sales.event_id,
      event_title: ticket_sales.event_title,
      sold_tickets: ticket_sales.sold_tickets,
      sold_extras: ticket_sales.sold_extras,
      sold_products: ticket_sales.sold_tickets + ticket_sales.sold_extras,
      gross_sales: ticket_sales.gross_sales
    }
  end

  defp show_event_ticket_sales_data(ticket_sales) do
    %{
      ticket_category_id: ticket_sales.ticket_category_id,
      ticket_category_name: ticket_sales.ticket_category_name,
      unit_price: ticket_sales.unit_price,
      voucher_description: ticket_sales.voucher_description,
      voucher_value: ticket_sales.voucher_value,
      voucher_code: ticket_sales.voucher_code,
      unit_price_voucher: ticket_sales.unit_price - ticket_sales.discount,
      sold_tickets: ticket_sales.sold_tickets,
      sold_extras: ticket_sales.sold_extras,
      sold_products: ticket_sales.sold_tickets + ticket_sales.sold_extras,
      gross_sales_total: ticket_sales.gross_sales,
      admission: ticket_sales.admission,
      multi_level_pricing_modifier_id: ticket_sales.mlpm_id,
      multi_level_pricing_modifier_label: ticket_sales.mlpm_label
    }
  end

  defp show_total_sales_data(total_sales) do
    sold_tickets = total_sales.sold_tickets || 0
    sold_extras = total_sales.sold_extras || 0

    %{
      sold_tickets: sold_tickets,
      sold_extras: sold_extras,
      sold_products: sold_tickets + sold_extras,
      gross_sales: total_sales.gross_sales || 0
    }
  end

  defp show_sold_tickets_for_event_data(ticket_sales) do
    # styler:sort
    %{
      admission: ticket_sales.admission,
      distribution_type: ticket_sales.distribution_type,
      mlpm_id: ticket_sales.mlpm_id,
      mlpm_label: ticket_sales.mlpm_label,
      quota: ticket_sales.quota,
      sold: ticket_sales.sold_products,
      ticket_category_id: ticket_sales.category_id,
      ticket_category_name: ticket_sales.category_name,
      unit_price: ticket_sales.unit_price,
      variant_id: ticket_sales.variant_id,
      variant_name: ticket_sales.category_name
    }
  end
end
