defmodule OrdersServiceWeb.WebhookController do
  use OrdersServiceWeb, :controller

  alias OrdersService.Adyen.EventHandler

  require Logger

  action_fallback OrdersServiceWeb.FallbackController

  # We only care about the first notification item because
  # JSON and HTTP POST webhooks contain only one NotificationRequestItem object in the notificationsItems array.
  def notification(conn, %{"notificationItems" => [notification_item | _]}) do
    Logger.debug("Received webhook notification item: #{inspect(notification_item)}")

    event_code = notification_item["NotificationRequestItem"]["eventCode"]
    merchant_reference = notification_item["NotificationRequestItem"]["merchantReference"]

    Logger.info(
      "Received #{inspect(event_code)} webhook notification item with merchantReference: #{inspect(merchant_reference)}"
    )

    with {:item, %{"NotificationRequestItem" => %{"eventCode" => event_code} = item}} <-
           {:item, notification_item},
         {{:ok, handler}, _event_code} <- {EventHandler.handler_for(event_code), event_code},
         :ok <- handler.handle(item) do
      Logger.debug("Successfully handled notification item: #{inspect(notification_item)}")

      Logger.info(
        "Successfully handled #{inspect(event_code)} webhook notification item with merchantReference: #{inspect(merchant_reference)}"
      )
    else
      {:item, notification_item} ->
        Logger.error("Received invalid notification item: #{inspect(notification_item)}")

      {{:error, :no_handler_found}, event_code} ->
        Logger.error("No handler found for event code: #{inspect(event_code)}")

      {:error, msg} ->
        Logger.error("Error handling notification item: #{inspect(notification_item)} with error: #{inspect(msg)}")

      _error ->
        Logger.error("Error handling notification item: #{inspect(notification_item)}")
    end

    conn
    |> put_status(:ok)
    |> text("[accepted]")
  end

  def notification(conn, params) do
    Logger.error("Received invalid webhook notification: #{inspect(params)}")

    conn
    |> put_status(:ok)
    |> text("[accepted]")
  end
end
