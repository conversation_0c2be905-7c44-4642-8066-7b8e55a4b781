defmodule OrdersService.Report.SettledPayment do
  @moduledoc false
  use Ecto.Schema

  import Ecto.Changeset

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id
  schema "settled_payments" do
    field :type, :string
    field :merchant_account, :string
    field :company_account, :string
    field :psp_reference, :string
    field :merchant_reference, Ecto.UUID
    field :payment_method, :string
    field :creation_date, :utc_datetime
    field :timezone, :string
    field :modification_references, :string
    field :gross_currency, :string
    field :gross_debit, :float
    field :gross_credit, :float
    field :exchange_rate, :float
    field :net_currency, :string
    field :net_debit, :float
    field :net_credit, :float
    field :commission, :float
    field :markup, :float
    field :scheme_fees, :float
    field :interchange, :float
    field :payment_method_variant, :string
    field :modification_merchant_reference, :string
    field :batch_number, :integer

    timestamps()
  end

  @doc false
  def changeset(settled_payment, attrs) do
    cast(settled_payment, attrs, [
      :company_account,
      :merchant_account,
      :psp_reference,
      :merchant_reference,
      :payment_method,
      :creation_date,
      :timezone,
      :type,
      :modification_references,
      :gross_currency,
      :gross_debit,
      :gross_credit,
      :exchange_rate,
      :net_currency,
      :net_debit,
      :net_credit,
      :commission,
      :markup,
      :scheme_fees,
      :interchange,
      :payment_method_variant,
      :modification_merchant_reference,
      :batch_number
    ])
  end

  @spec prepare_data(map()) :: map()
  def prepare_data(attrs) do
    %{
      company_account: attrs["Company Account"],
      merchant_account: attrs["Merchant Account"],
      psp_reference: attrs["Psp Reference"],
      merchant_reference: maybe_parse_merchant_reference(attrs["Merchant Reference"]),
      payment_method: attrs["Payment Method"],
      creation_date: maybe_parse_date(attrs["Creation Date"]),
      timezone: attrs["TimeZone"],
      type: attrs["Type"],
      modification_references: attrs["Modification Reference"],
      gross_currency: attrs["Gross Currency"],
      gross_debit: maybe_parse_float(attrs["Gross Debit (GC)"]),
      gross_credit: maybe_parse_float(attrs["Gross Credit (GC)"]),
      exchange_rate: maybe_parse_float(attrs["Exchange Rate"]),
      net_currency: attrs["Net Currency"],
      net_debit: maybe_parse_float(attrs["Net Debit (NC)"]),
      net_credit: maybe_parse_float(attrs["Net Credit (NC)"]),
      commission: maybe_parse_float(attrs["Commission (NC)"]),
      markup: maybe_parse_float(attrs["Markup (NC)"]),
      scheme_fees: maybe_parse_float(attrs["Scheme Fees (NC)"]),
      interchange: maybe_parse_float(attrs["Interchange (NC)"]),
      payment_method_variant: attrs["Payment Method Variant"],
      modification_merchant_reference: attrs["Modification Merchant Reference"],
      batch_number: maybe_parse_integer(attrs["Batch Number"]),
      inserted_at: NaiveDateTime.truncate(NaiveDateTime.utc_now(), :second),
      updated_at: NaiveDateTime.truncate(NaiveDateTime.utc_now(), :second)
    }
  end

  defp maybe_parse_merchant_reference(nil), do: "00000000-0000-0000-0000-000000000000"

  defp maybe_parse_merchant_reference(value) do
    case Ecto.UUID.cast(value) do
      {:ok, uuid} -> uuid
      _ -> "00000000-0000-0000-0000-000000000000"
    end
  end

  defp maybe_parse_date(nil), do: nil

  defp maybe_parse_date(value) do
    case NaiveDateTime.from_iso8601(value) do
      {:ok, datetime} ->
        datetime
        |> DateTime.from_naive("Etc/UTC")
        |> elem(1)

      _ ->
        nil
    end
  end

  defp maybe_parse_float(value) do
    case Float.parse(value) do
      {number, ""} -> number
      _ -> nil
    end
  end

  defp maybe_parse_integer(value) do
    case Integer.parse(value) do
      {number, ""} -> number
      _ -> nil
    end
  end
end
