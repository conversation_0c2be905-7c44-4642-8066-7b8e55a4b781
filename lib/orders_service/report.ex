defmodule OrdersService.Report do
  @moduledoc """
  The Report context.
  """

  import Ecto.Query, warn: false
  import OrdersService.Client.AdyenReportDownload

  alias NimbleCSV.RFC4180, as: CSV
  alias OrdersService.Repo
  alias OrdersService.Report.SettledPayment

  @spec process_report(map()) :: {:ok, String.t()} | {:error, String.t()}
  def process_report(%{"pspReference" => psp_reference, "merchantAccountCode" => merchant_account} = _item) do
    with {_, {:ok, report}} <- {:download, download_report(merchant_account, psp_reference)},
         {_, {:ok, list}} <- {:prepare, prepare_settled_payments(report)} do
      create_settled_payment_list(list)
      {:ok, "Report processed successfully"}
    else
      {:download, {:error, reason}} ->
        {:error, "Failed to download report: #{inspect(reason)}"}

      {:prepare, {:error, error}} ->
        {:error, "Failed to prepare settled payments: #{inspect(error)}"}
    end
  end

  defp prepare_settled_payments(reports) do
    list =
      reports
      |> CSV.parse_string(skip_headers: false)
      |> Stream.transform(nil, fn
        headers, nil -> {[], headers}
        row, headers -> {[headers |> Enum.zip(row) |> Map.new()], headers}
      end)
      |> Enum.to_list()
      |> Enum.map(fn row ->
        SettledPayment.prepare_data(row)
      end)

    {:ok, list}
  rescue
    error -> {:error, error}
  end

  defp create_settled_payment_list(attrs) do
    Repo.insert_all(SettledPayment, attrs)
  end
end
