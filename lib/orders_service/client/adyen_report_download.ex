defmodule OrdersService.Client.AdyenReportDownload do
  @moduledoc false
  use Tesla

  require Logger

  @spec client(Keyword.t()) :: Tesla.Client.t()
  def client(_opts \\ []) do
    middleware = [
      Tesla.Middleware.OpenTelemetry,
      Tesla.Middleware.JSON,
      {Tesla.Middleware.Logger, debug: true},
      {Tesla.Middleware.BaseUrl, "#{Keyword.get(config(), :report_download_url)}"},
      {Tesla.Middleware.Headers,
       [
         "x-api-key": get_api_key(),
         "Accept-Encoding": "gzip"
       ]}
    ]

    Tesla.client(middleware)
  end

  @spec download_report(String.t(), String.t()) ::
          {:ok, binary()} | {:error, any()}
  def download_report(merchant_account, psp_reference) do
    client()
    |> Tesla.get("/#{merchant_account}/#{psp_reference}")
    |> handle_response()
  end

  defp get_api_key do
    config()
    |> Keyword.get(:api_key)
    |> Keyword.get(:report_api_key)
  end

  defp config do
    Application.get_all_env(:adyen)
  end

  defp handle_response({:ok, %Tesla.Env{body: body, headers: headers}}) do
    case decompress_response(body, headers) do
      {:ok, body} ->
        {:ok, body}

      {:error, reason} ->
        Logger.error("Failed to decompress response: #{inspect(reason)}")
        {:error, reason}
    end
  end

  defp handle_response({:error, error}) do
    Logger.error("Failed to download report file: #{inspect(error)}")

    {:error, error}
  end

  defp handle_response(_response) do
    {:error, :not_found}
  end

  defp decompress_response(body, headers) do
    content_encoding = get_header_value(headers, "content-encoding")

    case content_encoding do
      "gzip" ->
        decompress_gzip(body)

      _ ->
        # Not compressed
        {:ok, body}
    end
  end

  defp decompress_gzip(compressed_data) when is_binary(compressed_data) do
    decompressed = :zlib.gunzip(compressed_data)
    {:ok, decompressed}
  rescue
    _ -> {:error, "Failed to decompress gzip data"}
  end

  defp get_header_value(headers, key) do
    headers
    |> Enum.find(fn {k, _v} -> String.downcase(k) == String.downcase(key) end)
    |> case do
      {_k, v} -> v
      nil -> nil
    end
  end
end
