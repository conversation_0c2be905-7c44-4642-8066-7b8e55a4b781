defmodule OrdersService.Workers.OrderMonitorWorker do
  @moduledoc """
  Oban worker that monitors order rates for systematic drops.

  It computes a rolling average of order counts per minute over the last `@lookback` minutes, and applies 3 sigma rule to detect anomalies.
  """

  use Oban.Worker, queue: :monitoring, max_attempts: 1

  alias OrdersService.Repo

  require Logger

  @lookback 60
  # sigma value
  @threshold 3

  @impl Oban.Worker
  def perform(_job) do
    Logger.debug("Performing order monitoring check...")

    result =
      @lookback
      |> orders_per_minute_with_zeros()
      |> Repo.query()

    case result do
      {:ok, %{rows: rows}} ->
        order_counts = Enum.map(rows, fn [_minute, count] -> count end)
        latest = List.last(order_counts)

        mean = average(order_counts)
        stddev = std_dev(order_counts, mean)

        # Only alert if orders have DROPPED significantly below normal ("latest < mean ..)")
        # Applies 3 sigma rule for detection, see https://en.wikipedia.org/wiki/68%E2%80%9395%E2%80%9399.7_rule
        if stddev > 0 and latest < mean - @threshold * stddev do
          Logger.critical("Purchase drop detected! Orders: #{latest}, Mean: #{mean}, StdDev: #{stddev}")
        else
          Logger.info("Nothing detected, good job! Orders: #{latest}, Mean: #{mean}, StdDev: #{stddev}")
        end

        :ok

      {:error, reason} ->
        Logger.error("Failed to query order counts: #{inspect(reason)}")
        :error
    end
  end

  defp average(list), do: Enum.sum(list) / length(list)

  defp std_dev(list, mean) do
    :math.sqrt(Enum.sum(Enum.map(list, fn x -> (x - mean) * (x - mean) end)) / length(list))
  end

  defp orders_per_minute_with_zeros(lookback) do
    # Returns a list of orders per minute for each minute from `lookback` minutes ago to now.
    """
    SELECT
      minutes.minute,
      COUNT(o.id) AS order_count
      FROM
      generate_series(
        date_trunc('minute', NOW()) - INTERVAL '#{lookback - 1} minutes',
        date_trunc('minute', NOW()),
        INTERVAL '1 minute'
      ) AS minutes(minute)
      LEFT JOIN orders o
      ON date_trunc('minute', o.inserted_at) = minutes.minute
      AND o.inserted_at >= minutes.minute
      AND o.inserted_at < minutes.minute + INTERVAL '1 minute'
      GROUP BY minutes.minute
      ORDER BY minutes.minute ASC;
    """
  end
end
