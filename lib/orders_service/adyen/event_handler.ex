defmodule OrdersService.Adyen.EventHandler do
  @moduledoc """
  Adyen notification event handler.
  """

  alias OrdersService.Adyen.EventHandler.AuthorizationEventHandler
  alias OrdersService.Adyen.EventHandler.CaptureEventHandler
  alias OrdersService.Adyen.EventHandler.RefundEventHandler
  alias OrdersService.Adyen.EventHandler.ReportEventHandler

  @doc """
  Handle Adyen notification event.
  """
  @callback handle(map) :: :ok | {:error, term}

  def handler_for("AUTHORISATION"), do: {:ok, AuthorizationEventHandler}
  def handler_for("REPORT_AVAILABLE"), do: {:ok, ReportEventHandler}

  def handler_for(event_code) when event_code in ["CAPTURE", "CAPTURE_FAILED"], do: {:ok, CaptureEventHand<PERSON>}

  def handler_for(event_code) when event_code in ["REFUND", "REFUND_FAILED", "REFUNDED_REVERSED", "CANCEL_OR_REFUND"],
    do: {:ok, RefundEvent<PERSON>and<PERSON>}

  def handler_for(_), do: {:error, :no_handler_found}
end
