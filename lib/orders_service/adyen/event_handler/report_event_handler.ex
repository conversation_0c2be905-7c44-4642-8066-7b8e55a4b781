defmodule OrdersService.Adyen.EventHandler.ReportEventHandler do
  @moduledoc false

  @behaviour OrdersService.Adyen.EventHandler

  alias OrdersService.Adyen.EventHandler
  alias OrdersService.Report

  require Logger

  @impl EventHandler
  def handle(%{"success" => "true", "eventCode" => "REPORT_AVAILABLE"} = item) do
    Logger.debug("Report event for item: #{inspect(item)} processing started")
    Report.process_report(item)
  end

  def handle(item) do
    Logger.error("Report event could not be processed for item: #{inspect(item)}")
    {:error, :report_event_not_successful}
  end
end
