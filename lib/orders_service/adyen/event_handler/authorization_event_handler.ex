defmodule OrdersService.Adyen.EventHandler.AuthorizationEventHandler do
  @moduledoc false

  @behaviour OrdersService.Adyen.EventHandler

  alias OrdersService.Adyen.EventHandler
  alias OrdersService.Order
  alias OrdersService.Orders.OrderProcessor
  alias OrdersService.PayinTransaction
  alias OrdersService.Payments.Payment
  alias OrdersService.Payments.Refund
  alias OrdersService.Pubsub.Publisher.OrdersPublisher
  alias OrdersService.Workers.OrderMailWorker
  alias OrdersService.Workers.TicketMailWorker

  require Logger

  @impl EventHandler
  def handle(item) do
    use_manual_capture? = Unleash.enabled?(:enable_manual_capture)
    do_handle(item, use_manual_capture?)
  end

  @doc """
    Handle broken but paid orders manually. Use it carefully!
    This function is used to handle orders that are in a PENDING state and need to be processed manually.
  """
  @spec handle_manual(order_id :: String.t()) :: :ok | {:error, atom()}
  @spec handle_manual(order_id :: String.t(), optional_paypal_email :: String.t() | nil) ::
          :ok | {:error, atom()}
  def handle_manual(order_id) do
    handle_manual(order_id, nil)
  end

  def handle_manual(order_id, optional_paypal_email) do
    with {:order, %Order{status: :PENDING} = order} <- {:order, Order.get(order_id)},
         {:order_update, {:ok, _data}} <-
           {:order_update, OrderProcessor.process_pending_order(order)},
         {:order_mail, {:ok, _}} <-
           {:order_mail, OrderMailWorker.queue(order, optional_paypal_email)},
         {:ticket_mail, {:ok, _}} <-
           {:ticket_mail, TicketMailWorker.queue(order, optional_paypal_email)},
         {:order_update, :ok} <-
           {:order_update, OrdersPublisher.publish_order_update(order_id)} do
      :ok
    else
      {:order, %Order{status: status}} ->
        Logger.error("Order with id #{order_id} is not in PENDING status, but in #{status} status")

        {:error, :order_not_pending}

      {:order, nil} ->
        Logger.error("Order not found")
        {:error, :order_not_found}

      {:order_update, error, _order, _item} ->
        Logger.error("Can't update order because of #{inspect(error)}")
        {:error, :cannot_update_order}

      {:order_mail, {:error, msg}} ->
        Logger.error(
          "Could not queue order confirmation mail for order with id #{order_id} because of error: #{inspect(msg)}"
        )

        {:error, :send_mail_failed}

      {:ticket_mail, {:error, msg}} ->
        Logger.error(
          "Could not queue order ticket mail for order with id #{order_id} because of error: #{inspect(msg)}"
        )

        {:error, :send_mail_failed}

      {:order_update, {:error, msg}} ->
        Logger.error("Could not publish order update for order with id #{order_id} because of error: #{inspect(msg)}")

        {:error, :publish_order_update_failed}
    end
  end

  defp do_handle(item, true) do
    optional_paypal_email = item["additionalData"]["paypalEmail"]
    psp_reference = item["pspReference"]

    capture_params = %{
      amount: %{
        value: item["amount"]["value"],
        currency: item["amount"]["currency"]
      },
      reference: item["merchantReference"]
    }

    with {:payin_transaction, {:ok, _payin_transaction}} <- {:payin_transaction, create_payin_transaction(item)},
         {:success, "true"} <- {:success, item["success"]},
         {:order, %Order{} = order} <- {:order, Order.get(item["merchantReference"], order_tickets: [:ticket])},
         {:refund, false, _order, _item} <- {:refund, needs_refund?(item, order), order, item},
         {:fetch_order_items, {:ok, items}} <- {:fetch_order_items, OrderProcessor.fetch_order_items(order)},
         {:order_update, {:ok, _data}, _order, _item} <-
           {:order_update, OrderProcessor.process_pending_order_with_lock(order, items), order, item},
         {:capture, {:ok, _response}, _order, _item} <-
           {:capture, Payment.capture_payment(capture_params, psp_reference), order, item},
         {:order_mail, {:ok, _}} <- {:order_mail, OrderMailWorker.queue(order, optional_paypal_email)},
         {:ticket_mail, {:ok, _}} <- {:ticket_mail, TicketMailWorker.queue(order, optional_paypal_email)},
         {:order_update, :ok} <- {:order_update, OrdersPublisher.publish_order_update(order.id)} do
      :ok
    else
      {:payin_transaction, {:error, msg}} ->
        Logger.error(
          "Could not create payin transaction for order with psp_reference: #{item["pspReference"]} because of error: #{inspect(msg)}"
        )

        {:error, :create_payin_transaction_failed}

      {:success, _} ->
        Logger.info("Authorization event was not successful for item: #{inspect(item)}")
        {:error, :authorization_event_not_successful}

      {:order, _order} ->
        Logger.error("Could not find a order with merchantReference: #{item["merchantReference"]}")

        {:error, :order_not_found}

      {:refund, true, order, item} ->
        handle_refund(order, item)

      {:fetch_order_items, {:error, error}} ->
        Logger.error(
          "Could not fetch order items for order with merchantReference: #{item["merchantReference"]} because of error: #{inspect(error)}"
        )

        {:error, :fetch_order_items_failed}

      {:order_update, error, order, item} ->
        handle_failed_order_update(error, order, item)

      {:capture, {:error, msg}, order, _item} ->
        Logger.error(
          "Could not capture payment for order with merchantReference: #{item["merchantReference"]} because of error: #{inspect(msg)}"
        )

        handle_failed_capture(order)

      {:order_mail, {:error, msg}} ->
        Logger.critical(
          "Could not queue order confirmation mail for order with merchantReference: #{item["merchantReference"]} because of error: #{inspect(msg)}"
        )

        {:error, :send_mail_failed}

      {:ticket_mail, {:error, msg}} ->
        Logger.critical(
          "Could not queue order ticket mail for order with merchantReference: #{item["merchantReference"]} because of error: #{inspect(msg)}"
        )

        {:error, :send_mail_failed}

      {:order_update, {:error, msg}} ->
        Logger.critical(
          "Could not publish order update for order with merchantReference: #{inspect(item["merchantReference"])} because of error: #{inspect(msg)}"
        )

        {:error, :publish_order_update_failed}

      {:validate_availability, {:error, error}} ->
        Logger.error(
          "Could not validate order items availability for order with merchantReference: #{item["merchantReference"]} because of error: #{inspect(error)}"
        )

        {:error, :validate_order_items_availability_failed}
    end
  end

  defp do_handle(item, false) do
    optional_paypal_email = item["additionalData"]["paypalEmail"]

    with {:payin_transaction, {:ok, _payin_transaction}} <-
           {:payin_transaction, create_payin_transaction(item)},
         {:success, "true"} <-
           {:success, item["success"]},
         {:order, %Order{} = order} <-
           {:order, Order.get(item["merchantReference"])},
         {:refund, false, _order, _item} <-
           {:refund, needs_refund?(item, order), order, item},
         {:order_update, {:ok, _data}, _order, _item} <-
           {:order_update, OrderProcessor.process_pending_order(order), order, item},
         {:order_mail, {:ok, _}} <-
           {:order_mail, OrderMailWorker.queue(order, optional_paypal_email)},
         {:ticket_mail, {:ok, _}} <-
           {:ticket_mail, TicketMailWorker.queue(order, optional_paypal_email)},
         {:order_update, :ok} <-
           {:order_update, OrdersPublisher.publish_order_update(order.id)} do
      :ok
    else
      {:payin_transaction, {:error, msg}} ->
        Logger.error(
          "Could not create payin transaction for order with psp_reference: #{item["pspReference"]} because of error: #{inspect(msg)}"
        )

        {:error, :create_payin_transaction_failed}

      {:success, _} ->
        Logger.info("Authorization event was not successful for item: #{inspect(item)}")
        {:error, :authorization_event_not_successful}

      {:order, _order} ->
        Logger.error("Could not find a order with merchantReference: #{item["merchantReference"]}")

        {:error, :order_not_found}

      {:refund, true, order, item} ->
        handle_refund(order, item)

      {:order_update, error, order, item} ->
        handle_failed_order_update(error, order, item)

      {:order_mail, {:error, msg}} ->
        Logger.critical(
          "Could not queue order confirmation mail for order with merchantReference: #{item["merchantReference"]} because of error: #{inspect(msg)}"
        )

        {:error, :send_mail_failed}

      {:ticket_mail, {:error, msg}} ->
        Logger.critical(
          "Could not queue order ticket mail for order with merchantReference: #{item["merchantReference"]} because of error: #{inspect(msg)}"
        )

        {:error, :send_mail_failed}

      {:order_update, {:error, msg}} ->
        Logger.critical(
          "Could not publish order update for order with merchantReference: #{inspect(item["merchantReference"])} because of error: #{inspect(msg)}"
        )

        {:error, :publish_order_update_failed}
    end
  end

  defp create_payin_transaction(item) do
    status =
      case item["success"] do
        "true" -> "SUCCESS"
        _ -> "FAILURE"
      end

    payin_transaction_attrs =
      %{
        status: status,
        currency: item["amount"]["currency"],
        amount: item["amount"]["value"],
        psp_reference: item["pspReference"],
        order_id: item["merchantReference"],
        psp: "adyen",
        psp_result: item,
        payment_method: item["paymentMethod"]
      }

    PayinTransaction.create(payin_transaction_attrs)
  end

  defp handle_refund(
         order,
         %{"pspReference" => _psp_reference, "amount" => %{"value" => _value, "currency" => _currency}} = item
       ) do
    if Unleash.enabled?(:enable_manual_capture) do
      Logger.debug(
        "Authorization event with manual capture enabled, skipping refund handling for item: #{inspect(item)}"
      )

      :ok
    else
      Refund.init_order_refund(
        order,
        item
      )
    end
  end

  defp handle_refund(_order, item) do
    Logger.error("Received an invalid notification item: #{inspect(item)}")
    {:error, :invalid_notification_item}
  end

  defp handle_failed_capture(%Order{} = order) do
    OrderProcessor.process_failed_capture_order(order)
  end

  defp handle_failed_order_update({:error, :inital_order, err, _}, _order, item) do
    Logger.error(
      "Could not get and lock order with merchantReference: #{inspect(item["merchantReference"])} because of error: #{inspect(err)}"
    )

    {:error, :get_and_lock_order_failed}
  end

  defp handle_failed_order_update({:error, :seats, _err, _}, order, item) do
    handle_refund(order, item)
  end

  defp handle_failed_order_update(error, _order, item) do
    Logger.critical(
      "Could not process order with merchantReference: #{inspect(item["merchantReference"])} because of error: #{inspect(error)}"
    )

    {:error, :process_order_failed}
  end

  defp needs_refund?(%{"pspReference" => psp_reference} = _item, %{status: :TIMEDOUT} = _order)
       when not is_nil(psp_reference),
       do: true

  defp needs_refund?(_item, _order), do: false
end
