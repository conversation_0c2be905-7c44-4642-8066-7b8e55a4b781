defmodule OrdersService.Orders.OrderValidator do
  @moduledoc false

  alias OrdersService.Location
  alias OrdersService.Orders.OrderHelper
  alias OrdersService.Seats.SeatsValidator
  alias OrdersService.Ticket
  alias OrdersService.Tickets.TicketsQuota

  require Logger

  def validate(order) do
    validate_items(order)
  end

  def validate_items_available(items) do
    items
    |> Enum.group_by(
      &{&1.variant.distributionType, &1.variant.ticketCategory.id, &1.variant.ticketCategory.admission,
       &1.variant.status}
    )
    |> Enum.map(fn {_key, [item | _] = grouped_items} ->
      %{
        variant: %{
          distributionType: distribution_type,
          ticketCategory: %{id: ticket_category_id, admission: admission},
          status: status,
          salesChannel: sales_channel
        }
      } = item

      validate_item_available(%{
        variant: %{
          distributionType: distribution_type,
          ticketCategory: %{id: ticket_category_id, admission: admission},
          status: status,
          salesChannel: sales_channel
        },
        amount: Enum.sum(Enum.map(grouped_items, & &1.amount))
      })
    end)
    |> Enum.reduce([], fn item, errors ->
      case item do
        :ok ->
          errors

        {:error, error} ->
          [error | errors]
      end
    end)
    |> case do
      [] -> :ok
      errors -> {:error, errors}
    end
  end

  defp validate_items(%{"items" => []}), do: {:error, :empty_order_items}

  defp validate_items(%{"items" => items} = order) do
    with :ok <- validate_multiple(&validate_event_variant_match/1, items),
         :ok <- validate_items_available(items),
         :ok <- validate_multiple(&validate_min_max_amount/1, items),
         :ok <- validate_personal_information_complete(order),
         :ok <- validate_address_complete(order),
         :ok <- SeatsValidator.validate(items) do
      :ok
    else
      error ->
        Logger.warning("Order validation failed: #{inspect(error)}")
        error
    end
  end

  defp validate_event_variant_match(%{event: %{id: event_id}, variant: %{eventId: event_id}}), do: :ok

  defp validate_event_variant_match(%{event: %{id: event_id}, variant: %{id: variant_id}}) do
    Logger.warning(
      "A user tried to buy a ticket for variant #{variant_id} that does not belong to the event #{event_id}"
    )

    {:error, :variant_not_belong_to_event}
  end

  defp validate_item_available(%{
         variant:
           %{
             status: "ACTIVE",
             distributionType: "REGULAR",
             ticketCategory: %{id: ticket_category_id, admission: admission}
           } = variant,
         amount: amount
       }) do
    with {_, %TicketsQuota{total_quota: total_quota, reserved_quota: reserved_quota}} <-
           {:ticket_quota, OrdersService.Tickets.Ticket.get_tickets_quota_by_id_and_type(ticket_category_id, :REGULAR)},
         existing_items = Ticket.count_existing_items_for_ticket_category_id(ticket_category_id, admission, false),
         reserved_existing_items =
           Ticket.count_existing_items_for_ticket_category_id(ticket_category_id, admission, true),
         {_, true} <-
           {:check_remaining_quota,
            total_quota - reserved_quota >=
              existing_items - reserved_existing_items + amount} do
      :ok
    else
      {:ticket_quota, _} ->
        Logger.critical("Can't find ticket quota for ticket category #{ticket_category_id}")
        {:error, :ticket_quota_not_found}

      {:check_remaining_quota, false} ->
        Logger.warning(
          "A user tried to buy #{amount} ticket(s) for regular variant #{inspect(variant)} that are not available"
        )

        {:error, :ticket_amount_not_available}
    end
  end

  defp validate_item_available(%{
         variant:
           %{status: "ACTIVE", distributionType: "SALES_CHANNEL", salesChannel: %{"id" => sales_channel_id}} = variant,
         amount: amount
       }) do
    with {_, %TicketsQuota{total_quota: total_quota}} <-
           {:ticket_quota,
            OrdersService.Tickets.Ticket.get_tickets_quota_by_id_and_type(sales_channel_id, :SALES_CHANNEL)},
         existing_items = Ticket.count_existing_items_for_distribution_type(sales_channel_id, :SALES_CHANNEL, false),
         {_, true} <-
           {:check_remaining_quota, total_quota >= existing_items + amount} do
      :ok
    else
      {:ticket_quota, _} ->
        Logger.critical("Can't find ticket quota for sales channel #{sales_channel_id}")
        {:error, :ticket_quota_not_found}

      {:check_remaining_quota, false} ->
        Logger.warning(
          "A user tried to buy #{amount} ticket(s) for sales channel variant #{inspect(variant)} that are not available"
        )

        {:error, :ticket_amount_not_available}
    end
  end

  defp validate_item_available(%{variant: %{status: _status}}), do: {:error, :ticket_amount_not_available}

  defp validate_min_max_amount(%{amount: amount, variant: %{minAmount: min_amount, maxAmount: max_amount}}),
    do:
      if((is_nil(min_amount) || amount >= min_amount) && (is_nil(max_amount) || amount <= max_amount),
        do: :ok,
        else: {:error, :invalid_min_max_amount}
      )

  defp validate_address_complete(%{"items" => items} = order) do
    with {_, true} <- {:billing_address, Location.valid_address?(order["billingAddress"])},
         {_, true} <-
           {:delivery_address,
            Location.valid_address?(order["deliveryAddress"], OrderHelper.hard_tickets_for_order_items?(items))} do
      :ok
    else
      {:billing_address, _} ->
        Logger.info("Billing address validation failed for #{inspect(order)}")
        {:error, :invalid_billing_address}

      {:delivery_address, _} ->
        Logger.info("Billing address validation failed for #{inspect(order)}")
        {:error, :invalid_delivery_address}
    end
  end

  defp validate_address_complete(_order), do: {:error, :missing_order_items}

  defp validate_personal_information_complete(%{"seller_id" => seller_id}) when seller_id not in ["", nil], do: :ok

  defp validate_personal_information_complete(%{"personalInformation" => personal_information})
       when not is_nil(personal_information) do
    with {_, true, true} <-
           {:name, valid_string?(personal_information["firstName"]) || valid_string?(personal_information["givenName"]),
            valid_string?(personal_information["familyName"]) || valid_string?(personal_information["lastName"])},
         {_, true} <- {:gender, Enum.member?(["0", "1", "2", "9"], personal_information["gender"])},
         {_, true} <- {:birthdate, valid_string?(personal_information["birthdate"])},
         {_, true} <- {:mail, valid_email?(personal_information["email"])},
         {_, true} <- {:country, Location.valid_country_iso?(personal_information["countryIso"])},
         {_, true} <-
           {:postal_code,
            Location.valid_postal_code?(%{
              country_iso: personal_information["countryIso"],
              postal_code: personal_information["postalCode"],
              city: personal_information["city"]
            })} do
      :ok
    else
      {:name, _, _} -> {:error, :invalid_name}
      {:gender, _} -> {:error, :invalid_gender}
      {:birthdate, _} -> {:error, :invalid_birthdate}
      {:mail, _} -> {:error, :invalid_mail}
      {:country, _} -> {:error, :invalid_country}
      {:postal_code, _} -> {:error, :invalid_postal_code}
    end
  end

  # deprecated
  # only needed for backward compatibility
  defp validate_personal_information_complete(%{
         "billingAddress" => billing_address,
         "userInfo" => userinfo,
         "email" => email
       }) do
    with {_, true, true} <-
           {:billing_address, valid_string?(billing_address["firstName"]), valid_string?(billing_address["lastName"])},
         {_, true} <- {:userinfo, valid_string?(userinfo["birthdate"])},
         {_, true} <- {:email, valid_email?(email)} do
      :ok
    else
      {:billing_address, _, _} ->
        Logger.info("Billing address validation failed for #{inspect(billing_address)}")
        {:error, :invalid_billing_address}

      {:userinfo, false} ->
        Logger.info("Userinfo validation failed for #{inspect(userinfo)}")
        {:error, :invalid_userinfo}

      {:email, false} ->
        Logger.info("Email validation failed for #{inspect(userinfo)}")
        {:error, :invalid_email}
    end
  end

  defp validate_personal_information_complete(%{"billingAddress" => _billing_address, "userInfo" => _userInfo}),
    do: {:error, :missing_email}

  defp validate_personal_information_complete(%{"userInfo" => _userinfo, "email" => _email}),
    do: {:error, :missing_address}

  defp validate_personal_information_complete(%{"billingAddress" => _billing_address, "email" => _email}),
    do: {:error, :missing_userinfo}

  defp validate_personal_information_complete(_order), do: {:error, :incomplete_personal_information}

  defp validate_multiple(func, items) do
    items
    |> Enum.reduce([], fn item, errors ->
      case func.(item) do
        :ok ->
          errors

        {:error, error} ->
          [error | errors]
      end
    end)
    |> case do
      [] -> :ok
      errors -> {:error, errors}
    end
  end

  defp valid_email?(nil), do: false

  defp valid_email?(email) do
    email_regex = ~r/^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/
    Regex.match?(email_regex, email)
  end

  defp valid_string?(value) when is_binary(value), do: String.trim(value) != ""
  defp valid_string?(_), do: false
end
