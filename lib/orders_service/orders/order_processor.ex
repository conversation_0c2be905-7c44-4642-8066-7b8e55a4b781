defmodule OrdersService.Orders.OrderProcessor do
  @moduledoc false

  use OpenTelemetryDecorator

  import Ecto.Query
  import OrdersService.Telemetry.Helpers

  alias Ecto.Multi
  alias ExServiceClient.Services.EventsService
  alias OrdersService.Order
  alias OrdersService.OrderHistory
  alias OrdersService.Orders.OrderValidator
  alias OrdersService.OrderTicket
  alias OrdersService.PromoterBill
  alias OrdersService.Pubsub.Publisher.OrdersPublisher
  alias OrdersService.Repo
  alias OrdersService.Ticket
  alias OrdersService.TicketHistory
  alias OrdersService.Workers.OrderMailWorker
  alias OrdersService.Workers.TicketCounterUpdateWorker
  alias OrdersService.Workers.TicketMailWorker

  require Logger

  @order_pending_status :PENDING
  @order_created_status :CREATED
  @order_pending_statuses [@order_pending_status, @order_created_status]

  @spec maybe_process_pending_order(%{
          events: list(),
          order: map()
        }) ::
          {:ok, any()}
          | {:error, :mixed_regular_draft_tickets}
          | {:partial_success, any(), :send_mail_failed}
          | any()

  @decorate with_span("orders.process", include: [:result])
  def maybe_process_pending_order(%{events: events, order: %{id: order_id}} = data) do
    O11y.set_attributes(order_id: order_id, event_count: events |> Map.keys() |> length())

    case classify_events(events) do
      # If all events from order are draft events, then process pending order as if it was a total 0 order
      :all_draft ->
        process_draft_order(data)

      :mixed ->
        Logger.error("Could not process order with id: #{order_id} because cart contains regular and draft tickets.")
        {:error, :mixed_regular_draft_tickets}

      :none_draft ->
        process_regular_order(data)
    end
  end

  @spec validate_order_items_availability(order :: Order.t()) :: :ok | {:error, list()}
  def validate_order_items_availability(%Order{} = order) do
    with {:extract_variant, {:ok, variants}} <- {:extract_variant, extract_variant_from_order_items(order)},
         {:grouped_variants, items} <- {:grouped_variants, group_variants(variants)},
         {:validate_available, :ok} <- {:validate_available, OrderValidator.validate_items_available(items)} do
      Logger.debug("Order items are available for order with id: #{order.id}")
      :ok
    else
      {:extract_variant, {:error, errors}} ->
        Logger.error("Could not extract variants from order items: #{inspect(errors)}")
        {:error, errors}

      {:validate_available, {:error, errors}} ->
        Logger.error("Order items are not available: #{inspect(errors)}")
        {:error, errors}
    end
  end

  @spec fetch_order_items(order :: Order.t()) :: {:ok, map()} | {:error, list()}
  def fetch_order_items(%Order{} = order) do
    case extract_variant_from_order_items(order) do
      {:ok, variants} ->
        Logger.debug("Order items are available for order with id: #{order.id}")
        {:ok, group_variants(variants)}

      {:error, errors} ->
        Logger.error("Could not extract variants from order items: #{inspect(errors)}")
        {:error, errors}
    end
  end

  def process_pending_order(%Order{id: order_id} = order) do
    process_pending_order(order, Ticket.get_all_by_order_id(order_id))
  end

  def process_pending_order(order_id) do
    case Ecto.UUID.cast(order_id) do
      {:ok, _uuid} ->
        order = Order.get(order_id)
        process_pending_order(order)

      _error ->
        {:error, :no_valid_order_id}
    end
  end

  def process_pending_order(%Order{status: status, id: order_id}, tickets) when status in @order_pending_statuses do
    now = DateTime.utc_now()

    result =
      Multi.new()
      |> get_and_lock_order(order_id)
      |> Multi.update(:order, fn %{inital_order: order} ->
        Order.changeset(order, %{status: :PAID, paid_date: now, updated_at: now})
      end)
      |> Multi.insert(:order_history, fn %{inital_order: order} ->
        OrderHistory.changeset(%OrderHistory{}, %{
          order_id: order_id,
          status_before: order.status,
          status_after: :PAID,
          actor_type: :SYSTEM,
          inserted_at: now
        })
      end)
      |> update_tickets(tickets, now)
      |> then(&trace_db_transaction("order_processing", fn -> Repo.transaction(&1) end))

    case TicketCounterUpdateWorker.enqueue(tickets) do
      :ok ->
        Logger.debug("Successfully enqueued ticket counter updates for order #{order_id}")

      {:error, error} ->
        Logger.warning("Failed to enqueue ticket counter updates for order #{order_id}: #{inspect(error)}")
    end

    result
  end

  def process_pending_order(_order, _tickets), do: {:error, :order_not_pending}

  @decorate with_span("orders.process_with_lock", include: [:order_id])
  @spec process_pending_order_with_lock(Order.t(), [map()]) ::
          {:ok, any()} | {:error, any(), any(), any()}
  def process_pending_order_with_lock(%Order{status: status, id: order_id}, items)
      when status in @order_pending_statuses do
    now = DateTime.utc_now()

    result =
      measure_duration("orders.process_with_lock_duration", fn ->
        Multi.new()
        |> get_and_lock_order_for_update(order_id)
        |> get_and_lock_tickets_for_update(order_id)
        |> Multi.run(:validate_items_availability, fn _repo, _changes ->
          do_validate_items_availability(items)
        end)
        |> Multi.update(:order, fn %{inital_order: order} ->
          Order.changeset(order, %{status: :PAID, paid_date: now, updated_at: now})
        end)
        |> Multi.insert(:order_history, fn %{inital_order: order, get_tickets: _tickets} ->
          OrderHistory.changeset(%OrderHistory{}, %{
            order_id: order_id,
            status_before: order.status,
            status_after: :PAID,
            actor_type: :SYSTEM,
            inserted_at: now
          })
        end)
        |> Multi.run(:update_tickets, fn repo, %{get_tickets: tickets} ->
          do_update_locked_tickets(tickets, repo, now, order_id)
        end)
        |> then(&trace_db_transaction("order_processing", fn -> Repo.transaction(&1) end))
      end)

    case result do
      {:ok, %{get_tickets: tickets}} ->
        case TicketCounterUpdateWorker.enqueue(tickets) do
          :ok ->
            Logger.debug("Successfully enqueued ticket counter updates for order #{order_id}")

          {:error, error} ->
            Logger.warning("Failed to enqueue ticket counter updates for order #{order_id}: #{inspect(error)}")
        end

        result

      _ ->
        result
    end
  end

  @spec process_failed_capture_order(Order.t()) ::
          {:ok, any()} | {:error, any(), any(), any()}
  def process_failed_capture_order(%Order{status: status, id: order_id} = order)
      when status in @order_pending_statuses do
    now = DateTime.utc_now()
    tickets = Ticket.get_all_by_order_id(order_id)

    result =
      Multi.new()
      |> Multi.update(:order, fn _changes ->
        Order.changeset(order, %{status: :FAILED, paid_date: nil, updated_at: now})
      end)
      |> Multi.insert(:order_history, fn %{order: _order} ->
        OrderHistory.changeset(%OrderHistory{}, %{
          order_id: order_id,
          status_before: status,
          status_after: :FAILED,
          actor_type: :SYSTEM,
          inserted_at: now
        })
      end)
      |> update_failed_tickets(tickets, now)
      |> Repo.transaction()

    case TicketCounterUpdateWorker.enqueue(tickets) do
      :ok ->
        Logger.debug("Successfully enqueued ticket counter updates for order #{order_id}")

      {:error, error} ->
        Logger.warning("Failed to enqueue ticket counter updates for order #{order_id}: #{inspect(error)}")
    end

    result
  end

  defp get_and_lock_order_for_update(multi, order_id) do
    Multi.run(multi, :inital_order, fn repo, _ ->
      try do
        query =
          from(
            o in Order,
            where: o.id == ^order_id,
            lock: "FOR UPDATE"
          )

        query
        |> repo.one()
        |> case do
          %Order{status: @order_pending_status} = order -> {:ok, order}
          %Order{status: @order_created_status} = order -> {:ok, order}
          _ -> {:error, :pending_order_not_found}
        end
      rescue
        e ->
          Logger.debug("Error fetching order with id: #{order_id} for update: #{inspect(e)}")
          {:error, e}
      end
    end)
  end

  defp get_and_lock_tickets_for_update(multi, order_id) do
    Multi.run(multi, :get_tickets, fn repo, _changes ->
      try do
        query =
          from(t in Ticket,
            as: :ticket,
            inner_join: ot in OrderTicket,
            on: t.id == ot.ticket_id,
            as: :order_ticket,
            where: ot.order_id == ^order_id,
            lock: "FOR UPDATE"
          )

        {:ok, repo.all(query)}
      rescue
        e ->
          Logger.debug("Error fetching tickets for order with id: #{order_id} for update: #{inspect(e)}")
          {:error, e}
      end
    end)
  end

  defp extract_variant_from_order_items(%Order{order_tickets: order_tickets}) do
    order_tickets
    |> Enum.reduce({[], []}, fn %{ticket: ticket}, {variants, errors} ->
      case EventsService.Variant.get(ticket.variant_id) do
        {:ok, body} -> {[body | variants], errors}
        {:error, error} -> {variants, [error | errors]}
      end
    end)
    |> case do
      {variants, []} ->
        {:ok, variants}

      {_variants, [_error | _rest] = errors} ->
        {:error, errors}
    end
  end

  defp group_variants(variants) do
    variants
    |> Enum.frequencies_by(& &1.id)
    |> Enum.map(fn {variant_id, amount} ->
      variant = Enum.find(variants, &(&1.id == variant_id))
      %{variant: variant, amount: amount}
    end)
  end

  defp do_update_locked_tickets(tickets, repo, now, order_id) do
    tickets
    |> update_lock_tickets(now)
    |> repo.transaction()
    |> case do
      {:ok, _result} ->
        {:ok, :tickets_updated}

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        Logger.error("Failed to update tickets for order #{order_id}: #{inspect(failed_value)}")
        {:error, failed_value}
    end
  end

  defp do_validate_items_availability(items) do
    case OrderValidator.validate_items_available(items) do
      :ok ->
        {:ok, :items_available}

      {:error, errors} ->
        Logger.error("Order items are not available: #{inspect(errors)}")
        {:error, errors}
    end
  end

  # Process pending order if total is 0 (free tickets)
  defp process_regular_order(%{order: _order, order_bill: %{total: 0}} = data), do: process_draft_order(data)

  # Process pending order if it comes from a seller (skip payment process)
  defp process_regular_order(%{order: %{id: order_id, seller_id: seller_id}, events: events} = data)
       when not is_nil(seller_id) and seller_id != "" do
    event_ids = for {event_id, _value} <- events, do: event_id

    case event_ids do
      [] ->
        Logger.error("Could not determine event_ids for order with id: #{order_id} and seller_id: #{seller_id}")
        {:error, :missing_event_id}

      _ ->
        check_seller_permission(seller_id, event_ids, data)
    end
  end

  defp process_regular_order(%{order: _order} = data), do: {:ok, data}

  defp check_seller_permission(seller_id, event_ids, data) do
    case EventsService.check_seller_event_permission(seller_id, event_ids) do
      {:ok, %{"has_permission" => true}} ->
        # Seller has permission to sell for all events
        process_draft_order(data)

      {:ok, %{"has_permission" => false}} ->
        # Seller does not have permission
        Logger.error(
          "Seller with id: #{seller_id} does not have permission to sell tickets for events with ids: #{inspect(event_ids)}"
        )

        {:error, :unauthorized_seller}

      {:error, reason} ->
        # Error checking permission
        Logger.critical("Error checking seller permission: #{inspect(reason)}")
        {:error, :permission_check_failed}
    end
  end

  defp process_draft_order(%{order: order} = data) do
    tickets =
      Enum.reduce(data, [], fn
        {_key, %Ticket{} = ticket}, acc ->
          [ticket | acc]

        {_key, _}, acc ->
          acc
      end)

    with {:process, {:ok, data} = res} <- {:process, process_pending_order(order, tickets)},
         {:order_publish, :ok} <- {:order_publish, OrdersPublisher.publish_order_update(order.id)},
         {:seller_skip, %{seller_id: seller_id}, _} when seller_id in ["", nil] <- {:seller_skip, order, res},
         {:order_mail, {:ok, _}, _} <- {:order_mail, maybe_queue_order_mail(data), data},
         {:ticket_mail, {:ok, _}, _} <- {:ticket_mail, maybe_queue_ticket_mail(data), data} do
      res
    else
      {:process, error} ->
        Logger.error("Could not process order with id: #{order.id} because of error: #{inspect(error)}")

        error

      {:order_publish, error} ->
        Logger.error(
          "Could not publish orders.orders message with order_id: #{order.id} because of error: #{inspect(error)}"
        )

        error

      # happy path, do not send emails if order comes from seller (booking office)
      {:seller_skip, _, res} ->
        res

      {:order_mail, {:error, msg}, data} ->
        Logger.critical(
          "Could not queue order confirmation mail for order with id: #{order.id} because of error: #{inspect(msg)}"
        )

        {:partial_success, data, :send_mail_failed}

      {:ticket_mail, {:error, msg}, data} ->
        Logger.critical(
          "Could not queue order ticket mail for order with merchantReference: #{order.id} because of error: #{inspect(msg)}"
        )

        {:partial_success, data, :send_mail_failed}
    end
  end

  defp get_and_lock_order(multi, order_id) do
    Multi.run(multi, :inital_order, fn repo, _ ->
      try do
        order_id
        |> Order.get_and_lock_for_update_query()
        |> repo.one()
        |> case do
          %Order{status: @order_pending_status} = order -> {:ok, order}
          %Order{status: @order_created_status} = order -> {:ok, order}
          _ -> {:error, :pending_order_not_found}
        end
      rescue
        e ->
          {:error, e}
      end
    end)
  end

  defp update_lock_tickets(tickets, timestamp) do
    now = DateTime.utc_now()

    Enum.reduce(tickets, Multi.new(), fn %{id: ticket_id} = ticket, multi ->
      ticket_changeset =
        Ticket.changeset(ticket, %{
          status: :ACTIVE,
          purchase_date: timestamp
        })

      ticket_history_changeset =
        TicketHistory.changeset(%TicketHistory{}, %{
          ticket_id: ticket_id,
          status_before: ticket.status,
          status_after: :ACTIVE,
          actor_type: :SYSTEM,
          inserted_at: now
        })

      multi
      |> update_promoter_bill(ticket_id)
      |> Multi.update("ticket_#{ticket_id}", ticket_changeset)
      |> Multi.insert("ticket_history_#{ticket_id}", ticket_history_changeset)
    end)
  end

  defp update_tickets(multi, tickets, timestamp) do
    now = DateTime.utc_now()

    Enum.reduce(tickets, multi, fn %{id: ticket_id} = ticket, multi ->
      ticket_changeset =
        Ticket.changeset(ticket, %{
          status: :ACTIVE,
          purchase_date: timestamp
        })

      ticket_history_changeset =
        TicketHistory.changeset(%TicketHistory{}, %{
          ticket_id: ticket_id,
          status_before: ticket.status,
          status_after: :ACTIVE,
          actor_type: :SYSTEM,
          inserted_at: now
        })

      multi
      |> update_promoter_bill(ticket_id)
      |> Multi.update("ticket_#{ticket_id}", ticket_changeset)
      |> Multi.insert("ticket_history_#{ticket_id}", ticket_history_changeset)
    end)
  end

  defp update_failed_tickets(multi, tickets, timestamp) do
    now = DateTime.utc_now()

    Enum.reduce(tickets, multi, fn %{id: ticket_id, status: status} = ticket, multi ->
      ticket_changeset =
        Ticket.changeset(ticket, %{
          status: :FAILED,
          purchase_date: timestamp
        })

      ticket_history_changeset =
        TicketHistory.changeset(%TicketHistory{}, %{
          ticket_id: ticket_id,
          status_before: status,
          status_after: :FAILED,
          actor_type: :SYSTEM,
          inserted_at: now
        })

      multi
      |> update_failed_promoter_bill(ticket_id)
      |> Multi.update("ticket_#{ticket_id}", ticket_changeset)
      |> Multi.insert("ticket_history_#{ticket_id}", ticket_history_changeset)
    end)
  end

  defp update_failed_promoter_bill(multi, ticket_id) do
    case PromoterBill.get_for_ticket_id(ticket_id) do
      nil ->
        multi

      promoter_bill ->
        promoter_bill_changeset =
          PromoterBill.changeset(promoter_bill, %{status: @order_pending_status, updated_at: DateTime.utc_now()})

        Multi.update(multi, "promoter_bill_#{promoter_bill.id}", promoter_bill_changeset)
    end
  end

  defp update_promoter_bill(multi, ticket_id) do
    case PromoterBill.get_for_ticket_id(ticket_id) do
      nil ->
        multi

      promoter_bill ->
        promoter_bill_changeset =
          PromoterBill.changeset(promoter_bill, %{status: :PAID, updated_at: DateTime.utc_now()})

        Multi.update(multi, "promoter_bill_#{promoter_bill.id}", promoter_bill_changeset)
    end
  end

  defp maybe_queue_order_mail(%{order: order} = data) do
    context = build_email_context(data, :order_confirmation)

    if email_disabled?(context) do
      Logger.debug("Order confirmation email disabled for order #{order.id} via feature flag")
      {:ok, :skipped}
    else
      OrderMailWorker.queue(order)
    end
  end

  defp maybe_queue_ticket_mail(%{order: order} = data) do
    context = build_email_context(data, :ticket)

    if email_disabled?(context) do
      Logger.debug("Ticket email disabled for order #{order.id} via feature flag")
      {:ok, :skipped}
    else
      TicketMailWorker.queue(order)
    end
  end

  defp build_email_context(%{order: order} = data, email_type) do
    %{
      environment: Application.get_env(:orders_service, :environment),
      email_type: email_type,
      order_id: order.id,
      user_id: order.created_by,
      event_ids: extract_event_ids_from_data(data)
    }
  end

  defp email_disabled?(context) do
    Unleash.enabled?(:disable_all_draft_emails, context) ||
      Unleash.enabled?(String.to_atom("disable_draft_email_#{context.email_type}"), context) ||
      event_emails_disabled?(context)
  end

  defp event_emails_disabled?(%{event_ids: event_ids} = context) do
    Enum.any?(event_ids, fn event_id ->
      context_with_event = Map.put(context, :event_id, event_id)
      Unleash.enabled?(:disable_draft_event_emails, context_with_event)
    end)
  end

  defp extract_event_ids_from_data(data) do
    data
    |> Enum.reduce(MapSet.new(), fn
      {_key, %Ticket{event_id: event_id}}, acc when not is_nil(event_id) ->
        MapSet.put(acc, event_id)

      {_key, _}, acc ->
        acc
    end)
    |> MapSet.to_list()
  end

  # Used to check whether the cart only contains events of the same type (regular/draft).
  # Examples:

  # $ OrdersService.Orders.OrderProcessor.classify_events([{nil, %{isDraft: false}}, {nil, %{isDraft: false}}])
  # :none_draft
  # $ OrdersService.Orders.OrderProcessor.classify_events([{nil, %{isDraft: false}}, {nil, %{isDraft: true}}])
  # :mixed
  # $ OrdersService.Orders.OrderProcessor.classify_events([{nil, %{isDraft: true}}, {nil, %{isDraft: true}}])
  # :all_draft
  defp classify_events(events) do
    # `events` is a list of tuples containing {:event_id, event} from previous usage
    Enum.reduce_while(events, nil, fn
      # nil is the starting value and is being replaced by either `all_draft` or `none_draft` after the first event
      {_, %{isDraft: true}}, nil -> {:cont, :all_draft}
      {_, %{isDraft: false}}, nil -> {:cont, :none_draft}
      # check whether the following events match the first
      {_, %{isDraft: true}}, :all_draft -> {:cont, :all_draft}
      {_, %{isDraft: false}}, :none_draft -> {:cont, :none_draft}
      # case if we found mismatching event types
      _, _ -> {:halt, :mixed}
    end)
  end
end
