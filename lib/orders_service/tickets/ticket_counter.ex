defmodule OrdersService.Tickets.TicketCounter do
  @moduledoc """
  Handles ticket counter updates and publishes messages to the PubSub system.
  """
  import Ecto.Query

  alias OrdersService.Bill
  alias OrdersService.Order
  alias OrdersService.OrderTicket
  alias OrdersService.Repo
  alias OrdersService.Ticket
  alias OrdersService.Tickets.TicketsQuota

  require Logger

  @doc """
      Retrieves ticket counter data based on the provided filter parameters.

      ## Params
      - filter_params:
        - filter_type: atom(), allowed values: :category_id, :event_id, :variant_id, :voucher_id 
        - filter_value: Ecto.UUID.t(), ID of the type you want to filter
  """
  @spec get_ticket_counter(filter_params :: map()) ::
          map() | {:error, :unknown_filter_type}
  def get_ticket_counter(%{filter_type: :event_id, filter_value: event_id} = filter_params) do
    {completed_sales, pending_sales, sold_tickets, pending_tickets, existing_tickets, sold_extras, pending_extras,
     existing_extras, redeemed_extras, total_checked_in, _reserved_existing_items, _reserved_pending_items,
     _reserved_sold_items} = get_ticket_counter_data(filter_params)

    # styler:sort
    %{
      completed_sales: completed_sales,
      event_id: event_id,
      existing_extras: existing_extras,
      existing_tickets: existing_tickets,
      pending_extras: pending_extras,
      pending_sales: pending_sales,
      pending_tickets: pending_tickets,
      redeemed_extras: redeemed_extras,
      sold_extras: sold_extras,
      sold_for_popular_events: count_for_popular_events(event_id),
      sold_tickets: sold_tickets,
      timestamp: DateTime.utc_now(),
      total_checked_in: total_checked_in
    }
  end

  def get_ticket_counter(%{filter_type: :variant_id, filter_value: variant_id} = filter_params) do
    {completed_sales, pending_sales, sold_tickets, pending_tickets, existing_tickets, sold_extras, pending_extras,
     existing_extras, _redeemed_extras, _total_checked_in, _reserved_existing_items, _reserved_pending_items,
     _reserved_sold_items} = get_ticket_counter_data(filter_params)

    # styler:sort
    %{
      existing_items: existing_tickets + existing_extras,
      reserved_items: pending_tickets + pending_extras,
      sold: sold_tickets + pending_tickets + sold_extras + pending_extras,
      sold_items: sold_tickets + sold_extras,
      timestamp: DateTime.utc_now(),
      total_sales: completed_sales + pending_sales,
      variant_id: variant_id
    }
  end

  def get_ticket_counter(%{filter_type: :category_id, filter_value: ticket_category_id} = filter_params) do
    {completed_sales, pending_sales, sold_tickets, pending_tickets, existing_tickets, sold_extras, pending_extras,
     existing_extras, _redeemed_extras, _total_checked_in, reserved_existing_items, reserved_pending_items,
     reserved_sold_items} = get_ticket_counter_data(filter_params)

    # styler:sort
    %{
      existing_items: existing_tickets + existing_extras,
      existing_reserved_items: reserved_existing_items,
      pending_items: pending_tickets + pending_extras,
      pending_reserved_items: reserved_pending_items,
      sold: existing_tickets + existing_extras,
      sold_items: sold_tickets + sold_extras,
      sold_reserved_items: reserved_sold_items,
      ticket_category_id: ticket_category_id,
      timestamp: DateTime.utc_now(),
      total_sales: completed_sales + pending_sales
    }
  end

  def get_ticket_counter(%{filter_type: :voucher_id, filter_value: voucher_id} = filter_params) do
    %{
      voucher_id: voucher_id,
      used: count_tickets(filter_params),
      timestamp: DateTime.utc_now()
    }
  end

  def get_ticket_counter(filter_params) do
    Logger.error("Unknown filter type #{inspect(filter_params)} for ticket counter")
    {:error, :unknown_filter_type}
  end

  defp get_ticket_counter_data(filter_params) do
    {completed_sales, pending_sales} =
      get_sales(filter_params)

    {sold_tickets, pending_tickets, existing_tickets, sold_extras, pending_extras, existing_extras, redeemed_extras,
     total_checked_in, reserved_existing_items, reserved_pending_items,
     reserved_sold_items} = get_ticket_counts(filter_params)

    {completed_sales, pending_sales, sold_tickets, pending_tickets, existing_tickets, sold_extras, pending_extras,
     existing_extras, redeemed_extras, total_checked_in, reserved_existing_items, reserved_pending_items,
     reserved_sold_items}
  end

  defp get_sales(filter_params) do
    Enum.reduce(
      calculate_organizer_total(filter_params),
      {0, 0},
      fn
        %{order_status: :PENDING, organizer_total: total}, {completed_sales, pending_sales} ->
          {completed_sales, pending_sales + total}

        %{organizer_total: total}, {completed_sales, pending_sales} ->
          {completed_sales + total, pending_sales}
      end
    )
  end

  defp get_ticket_counts(filter_params) do
    Enum.reduce(
      count_tickets(filter_params),
      {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0},
      fn counter, acc -> merge_counter(counter, acc) end
    )
  end

  # merge one counter with another by adding the values
  # depending on the status and distribution type and maybe the reserved quota
  defp merge_counter(
         %{
           reserved_quota: true,
           distribution_type: :GUEST_LIST_INVITATION,
           admission: true,
           status: _status,
           count: count
         },
         {sold_tickets, pending_tickets, existing_tickets, sold_extras, pending_extras, existing_extras,
          redeemed_extras, total_checked_in, reserved_existing_items, reserved_pending_items, reserved_sold_items}
       ) do
    {sold_tickets, pending_tickets, existing_tickets + count, sold_extras, pending_extras, existing_extras,
     redeemed_extras, total_checked_in, reserved_existing_items + count, reserved_pending_items, reserved_sold_items}
  end

  defp merge_counter(
         %{
           reserved_quota: true,
           distribution_type: :GUEST_LIST_INVITATION,
           admission: false,
           status: _status,
           count: count
         },
         {sold_tickets, pending_tickets, existing_tickets, sold_extras, pending_extras, existing_extras,
          redeemed_extras, total_checked_in, reserved_existing_items, reserved_pending_items, reserved_sold_items}
       ) do
    {sold_tickets, pending_tickets, existing_tickets, sold_extras, pending_extras, existing_extras + count,
     redeemed_extras, total_checked_in, reserved_existing_items + count, reserved_pending_items, reserved_sold_items}
  end

  defp merge_counter(
         %{
           reserved_quota: true,
           distribution_type: _distribution_type,
           admission: true,
           status: :PENDING,
           count: count
         },
         {sold_tickets, pending_tickets, existing_tickets, sold_extras, pending_extras, existing_extras,
          redeemed_extras, total_checked_in, reserved_existing_items, reserved_pending_items, reserved_sold_items}
       ) do
    {sold_tickets, pending_tickets + count, existing_tickets + count, sold_extras, pending_extras, existing_extras,
     redeemed_extras, total_checked_in, reserved_existing_items + count, reserved_pending_items + count,
     reserved_sold_items}
  end

  defp merge_counter(
         %{
           reserved_quota: true,
           distribution_type: _distribution_type,
           admission: false,
           status: :PENDING,
           count: count
         },
         {sold_tickets, pending_tickets, existing_tickets, sold_extras, pending_extras, existing_extras,
          redeemed_extras, total_checked_in, reserved_existing_items, reserved_pending_items, reserved_sold_items}
       ) do
    {sold_tickets, pending_tickets, existing_tickets, sold_extras, pending_extras + count, existing_extras + count,
     redeemed_extras, total_checked_in, reserved_existing_items + count, reserved_pending_items + count,
     reserved_sold_items}
  end

  defp merge_counter(
         %{reserved_quota: true, distribution_type: _distribution_type, admission: true, status: :USED, count: count},
         {sold_tickets, pending_tickets, existing_tickets, sold_extras, pending_extras, existing_extras,
          redeemed_extras, total_checked_in, reserved_existing_items, reserved_pending_items, reserved_sold_items}
       ) do
    {sold_tickets + count, pending_tickets, existing_tickets + count, sold_extras, pending_extras, existing_extras,
     redeemed_extras, total_checked_in + count, reserved_existing_items + count, reserved_pending_items,
     reserved_sold_items + count}
  end

  defp merge_counter(
         %{reserved_quota: true, distribution_type: _distribution_type, admission: false, status: :USED, count: count},
         {sold_tickets, pending_tickets, existing_tickets, sold_extras, pending_extras, existing_extras,
          redeemed_extras, total_checked_in, reserved_existing_items, reserved_pending_items, reserved_sold_items}
       ) do
    {sold_tickets, pending_tickets, existing_tickets, sold_extras + count, pending_extras, existing_extras + count,
     redeemed_extras + count, total_checked_in, reserved_existing_items + count, reserved_pending_items,
     reserved_sold_items + count}
  end

  defp merge_counter(
         %{
           reserved_quota: true,
           distribution_type: _distribution_type,
           admission: false,
           status: _status,
           count: count
         },
         {sold_tickets, pending_tickets, existing_tickets, sold_extras, pending_extras, existing_extras,
          redeemed_extras, total_checked_in, reserved_existing_items, reserved_pending_items, reserved_sold_items}
       ) do
    {sold_tickets, pending_tickets, existing_tickets, sold_extras + count, pending_extras, existing_extras + count,
     redeemed_extras, total_checked_in, reserved_existing_items + count, reserved_pending_items,
     reserved_sold_items + count}
  end

  defp merge_counter(
         %{distribution_type: :GUEST_LIST_INVITATION, admission: true, status: _status, count: count},
         {sold_tickets, pending_tickets, existing_tickets, sold_extras, pending_extras, existing_extras,
          redeemed_extras, total_checked_in, reserved_existing_items, reserved_pending_items, reserved_sold_items}
       ) do
    {sold_tickets, pending_tickets, existing_tickets + count, sold_extras, pending_extras, existing_extras,
     redeemed_extras, total_checked_in, reserved_existing_items, reserved_pending_items, reserved_sold_items}
  end

  defp merge_counter(
         %{distribution_type: :GUEST_LIST_INVITATION, admission: false, status: _status, count: count},
         {sold_tickets, pending_tickets, existing_tickets, sold_extras, pending_extras, existing_extras,
          redeemed_extras, total_checked_in, reserved_existing_items, reserved_pending_items, reserved_sold_items}
       ) do
    {sold_tickets, pending_tickets, existing_tickets, sold_extras, pending_extras, existing_extras + count,
     redeemed_extras, total_checked_in, reserved_existing_items, reserved_pending_items, reserved_sold_items}
  end

  defp merge_counter(
         %{distribution_type: _distribution_type, admission: true, status: :PENDING, count: count},
         {sold_tickets, pending_tickets, existing_tickets, sold_extras, pending_extras, existing_extras,
          redeemed_extras, total_checked_in, reserved_existing_items, reserved_pending_items, reserved_sold_items}
       ) do
    {sold_tickets, pending_tickets + count, existing_tickets + count, sold_extras, pending_extras, existing_extras,
     redeemed_extras, total_checked_in, reserved_existing_items, reserved_pending_items, reserved_sold_items}
  end

  defp merge_counter(
         %{distribution_type: _distribution_type, admission: false, status: :PENDING, count: count},
         {sold_tickets, pending_tickets, existing_tickets, sold_extras, pending_extras, existing_extras,
          redeemed_extras, total_checked_in, reserved_existing_items, reserved_pending_items, reserved_sold_items}
       ) do
    {sold_tickets, pending_tickets, existing_tickets, sold_extras, pending_extras + count, existing_extras + count,
     redeemed_extras, total_checked_in, reserved_existing_items, reserved_pending_items, reserved_sold_items}
  end

  defp merge_counter(
         %{distribution_type: _distribution_type, admission: true, status: :USED, count: count},
         {sold_tickets, pending_tickets, existing_tickets, sold_extras, pending_extras, existing_extras,
          redeemed_extras, total_checked_in, reserved_existing_items, reserved_pending_items, reserved_sold_items}
       ) do
    {sold_tickets + count, pending_tickets, existing_tickets + count, sold_extras, pending_extras, existing_extras,
     redeemed_extras, total_checked_in + count, reserved_existing_items, reserved_pending_items, reserved_sold_items}
  end

  defp merge_counter(
         %{distribution_type: _distribution_type, admission: false, status: :USED, count: count},
         {sold_tickets, pending_tickets, existing_tickets, sold_extras, pending_extras, existing_extras,
          redeemed_extras, total_checked_in, reserved_existing_items, reserved_pending_items, reserved_sold_items}
       ) do
    {sold_tickets, pending_tickets, existing_tickets, sold_extras + count, pending_extras, existing_extras + count,
     redeemed_extras + count, total_checked_in, reserved_existing_items, reserved_pending_items, reserved_sold_items}
  end

  defp merge_counter(
         %{reserved_quota: true, distribution_type: _distribution_type, admission: true, status: _status, count: count},
         {sold_tickets, pending_tickets, existing_tickets, sold_extras, pending_extras, existing_extras,
          redeemed_extras, total_checked_in, reserved_existing_items, reserved_pending_items, reserved_sold_items}
       ) do
    {sold_tickets + count, pending_tickets, existing_tickets + count, sold_extras, pending_extras, existing_extras,
     redeemed_extras, total_checked_in, reserved_existing_items + count, reserved_pending_items,
     reserved_sold_items + count}
  end

  defp merge_counter(
         %{distribution_type: _distribution_type, admission: true, status: _status, count: count},
         {sold_tickets, pending_tickets, existing_tickets, sold_extras, pending_extras, existing_extras,
          redeemed_extras, total_checked_in, reserved_existing_items, reserved_pending_items, reserved_sold_items}
       ) do
    {sold_tickets + count, pending_tickets, existing_tickets + count, sold_extras, pending_extras, existing_extras,
     redeemed_extras, total_checked_in, reserved_existing_items, reserved_pending_items, reserved_sold_items}
  end

  defp merge_counter(
         %{distribution_type: _distribution_type, admission: false, status: _status, count: count},
         {sold_tickets, pending_tickets, existing_tickets, sold_extras, pending_extras, existing_extras,
          redeemed_extras, total_checked_in, reserved_existing_items, reserved_pending_items, reserved_sold_items}
       ) do
    {sold_tickets, pending_tickets, existing_tickets, sold_extras + count, pending_extras, existing_extras + count,
     redeemed_extras, total_checked_in, reserved_existing_items, reserved_pending_items, reserved_sold_items}
  end

  defp merge_counter(counter_params, acc) do
    Logger.critical("Unexpected ticket counter data for #{inspect(counter_params)}. Please check the data integrity.")

    acc
  end

  defp calculate_organizer_total(%{filter_type: field, filter_value: value}) do
    order_ticket_query()
    |> join(:inner, [order_ticket: ot], b in Bill, on: ot.bill_id == b.id, as: :bill)
    |> filter_for_order_status([:PAID, :PENDING, :REFUND_PENDING])
    |> maybe_filter_ticket(:status, [:ACTIVE, :USED, :UNUSED, :PENDING, :SWAPPED])
    |> maybe_filter_ticket(field, value)
    |> group_by([order: o], o.status)
    |> select(
      [bill: b, order: o],
      %{
        order_status: o.status,
        organizer_total:
          sum(
            coalesce(b.promoter_total, 0) +
              coalesce(b.promoter_kickback, 0) +
              coalesce(b.promoter_kickback_tax, 0)
          )
      }
    )
    |> Repo.all()
  end

  # The voucher counter should be based on orders having that voucher instead of tickets as per SD1-5151
  defp count_tickets(%{filter_type: :voucher_id, filter_value: voucher_id}) do
    valid_tickets_query()
    |> join(:inner, [ticket: t], ot in OrderTicket, on: t.id == ot.ticket_id, as: :order_ticket)
    |> join(:inner, [order_ticket: ot], o in Order, on: ot.order_id == o.id, as: :order)
    |> maybe_filter_ticket(:voucher_id, voucher_id)
    |> select([order: o], count(fragment("DISTINCT ?", o.id)))
    |> Repo.one()
  end

  defp count_tickets(%{filter_type: :category_id, filter_value: ticket_category_id}) do
    valid_tickets_query()
    |> join(:left, [ticket: t], tq in TicketsQuota,
      on: t.distribution_type_id == tq.type_id,
      as: :ticket_quota
    )
    |> maybe_filter_ticket(:category_id, ticket_category_id)
    |> group_by([ticket: t, ticket_quota: tq], [
      t.status,
      t.distribution_type,
      t.admission,
      tq.reserved_quota
    ])
    |> select([ticket: t, ticket_quota: tq], %{
      status: t.status,
      admission: t.admission,
      distribution_type: t.distribution_type,
      reserved_quota: tq.reserved_quota > 0,
      count: count(t.id)
    })
    |> Repo.all()
  end

  defp count_tickets(%{filter_type: field, filter_value: value}) do
    valid_tickets_query()
    |> maybe_filter_ticket(field, value)
    |> group_by([ticket: t], [t.status, t.distribution_type, t.admission])
    |> select([ticket: t], %{
      status: t.status,
      admission: t.admission,
      distribution_type: t.distribution_type,
      count: count(t.id)
    })
    |> Repo.all()
  end

  defp count_for_popular_events(event_id) do
    order_ticket_query()
    |> filter_for_order_status([:PAID, :PENDING, :REFUND_PENDING])
    |> maybe_reject_tickets(
      :status,
      [
        :INVITATION_REJECTED,
        :FAILED,
        :REFUNDING,
        :REFUNDED,
        :SWAPPED,
        :TIMEDOUT,
        :CREATED
      ]
    )
    |> maybe_reject_tickets(:distribution_type, :GUEST_LIST_INVITATION)
    |> maybe_filter_ticket(:event_id, event_id)
    |> maybe_filter_ticket(:admission, true)
    |> filter_tickets_by_age(DateTime.add(DateTime.utc_now(), -3 * 24 * 60 * 60, :second))
    |> select([ticket: t], count(t.id))
    |> Repo.one() || 0
  end

  defp valid_tickets_query do
    from(t in Ticket,
      as: :ticket,
      where:
        t.status not in [
          :INVITATION_REJECTED,
          :FAILED,
          :REFUNDING,
          :REFUNDED,
          :SWAPPED,
          :TIMEDOUT,
          :DEFRAUDED,
          :CREATED
        ]
    )
  end

  defp order_ticket_query do
    from(t in Ticket,
      as: :ticket,
      left_join: ot in OrderTicket,
      on: t.id == ot.ticket_id,
      as: :order_ticket,
      left_join: o in Order,
      on: ot.order_id == o.id,
      as: :order
    )
  end

  defp maybe_filter_ticket(query, _field, nil), do: query

  defp maybe_filter_ticket(query, field, value) when is_list(value),
    do: where(query, [ticket: t], field(t, ^field) in ^value)

  defp maybe_filter_ticket(query, field, value), do: where(query, [ticket: t], field(t, ^field) == ^value)

  defp maybe_reject_tickets(query, _field, nil), do: query

  defp maybe_reject_tickets(query, field, value) when is_list(value),
    do: where(query, [ticket: t], field(t, ^field) not in ^value)

  defp maybe_reject_tickets(query, field, value), do: where(query, [ticket: t], field(t, ^field) != ^value)

  defp filter_tickets_by_age(query, min_age), do: where(query, [ticket: t], t.inserted_at >= ^min_age)

  defp filter_for_order_status(query, status) when is_list(status), do: where(query, [order: o], o.status in ^status)

  defp filter_for_order_status(query, status) when is_atom(status), do: where(query, [order: o], o.status == ^status)

  defp filter_for_order_status(query, _status), do: query
end
