defmodule EventsService.Vendor do
  @moduledoc """
  The Vendor context.
  """

  import Ecto.Query, warn: false

  alias Adyen.Services.BalancePlatform
  alias Adyen.Services.LegalEntityManagement
  alias Ecto.Multi
  alias EventsService.Addresses.Address
  alias EventsService.Events.Event
  alias EventsService.Events.NameShortener
  alias EventsService.Promotion.CouponCode
  alias EventsService.Repo
  alias EventsService.Sellers
  alias EventsService.Util.FirebaseClient
  alias EventsService.Vendor.Promoter
  alias EventsService.Vendor.PromoterEmployee
  alias EventsService.Vendor.PromoterUser
  alias EventsService.Worker.PromoterEmailWorker
  alias ExServiceClient.Services.BackendV2
  alias ExServiceClient.Services.ShortlinkService.Link

  require Logger

  @doc """
  Returns the list of promoters.

  ## Examples

      iex> list_promoters()
      [%Promoter{}, ...]

  """
  def list_promoters do
    Repo.all(Promoter)
  end

  @doc """
  Gets a single promoter.

  Raises `Ecto.NoResultsError` if the Promoter does not exist.

  ## Examples

      iex> get_promoter!(123)
      %Promoter{}

      iex> get_promoter!(456)
      ** (Ecto.NoResultsError)

  """
  def get_promoter!(id, preloads \\ []), do: Repo.one!(from(p in Promoter, where: p.id == ^id, preload: ^preloads))

  @spec get_promoter(id :: Ecto.UUID.t(), preloads :: list()) :: Promoter.t() | nil
  def get_promoter(id, preloads \\ []), do: get_organizer(id, preloads)

  @spec get_organizer(id :: Ecto.UUID.t(), preloads :: [atom()]) :: Promoter.t() | nil
  def get_organizer(id, preloads \\ []), do: Repo.one(from(p in Promoter, where: p.id == ^id, preload: ^preloads))

  def get_promoter_by_creator(user_id, preloads \\ []),
    do: Repo.one(from(p in Promoter, where: p.created_by_document_id == ^user_id, preload: ^preloads))

  def get_promoter_by_firestore_id(nil), do: %{id: nil, created_by_document_id: nil}

  def get_promoter_by_firestore_id(id) do
    query = from(promoter in Promoter, where: promoter.firestore_id == ^id)
    Repo.one(query)
  end

  def get_promoter_by_legal_entity(legal_entity_id, preloads \\ []) do
    query = from(promoter in Promoter, where: promoter.legal_entity_id == ^legal_entity_id, preload: ^preloads)
    Repo.one(query)
  end

  def get_promoter_by_id(id) do
    case Ecto.UUID.dump(id) do
      {:ok, _} -> Repo.one(from(promoter in Promoter, where: promoter.id == ^id))
      _ -> Repo.one(from(promoter in Promoter, where: promoter.firestore_id == ^id))
    end
  end

  @doc """
  Creates a promoter.
  Creates a promoter user
  Creates  promoter's legal entity
  Creates depending on the promoter type the legal entity and corresponding account holder and balance account.
  Update promoter with it balance account, account holder and legal entity id.
  ## Examples

      iex> create_promoter(%{field: value})
      {:ok, %Promoter{}}

      iex> create_promoter(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_promoter(attrs \\ %{}) do
    country = attrs["country"]
    short_code = String.upcase(NameShortener.generate_unique_promoter_shortcode())
    attrs = Map.put(attrs, "short_code", short_code)

    result =
      Multi.new()
      |> Multi.insert(:promoter, Promoter.create_changeset(%Promoter{}, attrs))
      |> Multi.insert(:promoter_user, fn %{promoter: promoter} ->
        PromoterUser.changeset(%PromoterUser{}, %{
          "user_document_id" => promoter.created_by_document_id,
          "promoter_id" => promoter.id
        })
      end)
      |> Multi.run(:legal_entity, fn _repo, %{promoter: promoter} ->
        with {_, {:ok, legal_entity}} <-
               {:create_legal_entity, create_promoter_legal_entity(promoter, country)},
             {_, {:ok, account_holder}} <-
               {:create_account_holder, create_account_holder(promoter, legal_entity)},
             {_, {:ok, balance_account}} <-
               {:create_balance_account, create_balance_account(promoter, account_holder)} do
          {:ok,
           %{
             legal_entity: legal_entity,
             account_holder: account_holder,
             balance_account: balance_account
           }}
        else
          {operation, {:error, msg}} -> {:error, {operation, msg}}
        end
      end)
      |> Multi.update(:update_promoter, fn %{
                                             promoter: promoter,
                                             legal_entity: %{
                                               legal_entity: legal_entity,
                                               account_holder: account_holder,
                                               balance_account: balance_account
                                             }
                                           } ->
        Promoter.changeset(promoter, %{
          "account_holder_id" => account_holder["id"],
          "balance_account_id" => balance_account["id"],
          "legal_entity_id" => legal_entity["id"]
        })
      end)
      |> Multi.run(:seller_organizer, fn _repo, %{update_promoter: promoter} ->
        create_organizer_seller(promoter)
      end)
      |> Repo.transaction()

    case result do
      {:ok, %{update_promoter: promoter}} ->
        {:ok, promoter}

      {:error, failed_operation, failed_value, _changes_so_far} ->
        Logger.error(
          "Failed to create promoter in step #{inspect(failed_operation)} and error #{inspect(failed_value)}"
        )

        {:error, failed_value}
    end
  end

  def create_promoter_address(address, promoter) do
    iso = String.trim(address["country"])

    payload =
      %{
        "country_iso" => iso,
        "locality" => address["city"],
        "postal_code" => address["zipCode"],
        "street_address" => "#{address["address"]} #{address["streetNumber"]}"
      }

    Multi.new()
    |> Multi.insert(:address, Address.changeset(%Address{}, payload))
    |> Multi.update(:promoter, fn %{address: address} ->
      Promoter.changeset(promoter, %{"address_id" => address.id, "is_verified" => true})
    end)
    |> Repo.transaction()
  end

  def update_promoter_address(_validation_status, %Promoter{address: nil} = promoter, attrs) do
    case create_promoter_address(attrs, promoter) do
      {:ok, %{address: address}} ->
        {:ok, address}

      {:error, _failed_operation, failed_value, _changes} ->
        Logger.error(
          "Could not create promoter address for promoter id: #{promoter.id} and error #{inspect(failed_value)}"
        )

        {:error, :create_promoter_address_failed}
    end
  end

  def update_promoter_address(true, %Promoter{address: %Address{} = address}, attrs) do
    payload =
      %{
        "country_iso" => attrs["country"],
        "locality" => attrs["city"],
        "postal_code" => attrs["zipCode"],
        "street_address" => "#{attrs["address"]} #{attrs["streetNumber"]}"
      }

    address
    |> Address.changeset(payload)
    |> Repo.update()
  end

  def update_promoter_address(false, %Promoter{} = promoter, _attrs) do
    Logger.error("Legal entity address is not complete or onboarding is not completed")

    promoter
    |> Promoter.changeset(%{"is_verified" => false})
    |> Repo.update()
  end

  def update_account_holder(%{
        "data" => %{"accountHolder" => %{"legalEntityId" => legal_entity_id}},
        "type" => "balancePlatform.accountHolder.updated"
      }) do
    with {_, %Promoter{} = promoter} <-
           {:get_promoter, get_promoter_by_legal_entity(legal_entity_id, [:address])},
         {_, {:ok, legal_entity}} <- {:get_legal_entity, get_legal_entity(legal_entity_id)},
         legal_entity_dto = legal_entity_to_promoter_dto(legal_entity),
         {_, validation} <- {:validate, check_complete_address_adyen(legal_entity_dto)},
         {_, {:ok, _updated_address}} <-
           {:update, update_promoter_address(validation, promoter, legal_entity_dto)} do
      :ok
    else
      {:get_promoter, nil} ->
        Logger.error("Could not find promoter for legal entity id: #{legal_entity_id}")
        {:error, :promoter_not_found}

      {:get_legal_entity, _} ->
        Logger.error("Could not find legal entity for legal entity id: #{legal_entity_id}")
        {:error, :legal_entity_not_found}

      {:update, _} ->
        Logger.error("Could not update promoter address for legal entity id: #{legal_entity_id}")
        {:error, :update_promoter_address_failed}
    end
  end

  def update_account_holder(_params), do: :ok

  def validate_legal_entity(%Promoter{legal_entity_id: "", id: id}), do: {:error, :no_legal_entity, id}
  def validate_legal_entity(%Promoter{} = promoter), do: validate_promoter_address(promoter)

  def validate_legal_entity(user_id) when is_binary(user_id) do
    case find_by_assigned_user(user_id) do
      [%PromoterUser{promoter: %Promoter{legal_entity_id: "", id: id}} | _] ->
        {:error, :no_legal_entity, id}

      [%PromoterUser{promoter: %Promoter{} = promoter} | _] ->
        validate_promoter_address(promoter)

      _ ->
        {:error, :no_promoter}
    end
  end

  def validate_legal_entity(_), do: {:error, :invalid}

  def get_legal_entity(legal_entity_id), do: LegalEntityManagement.get_legal_entity(legal_entity_id)

  def legal_entity_to_promoter_dto(%{"type" => "individual", "id" => _id, "individual" => individual} = _legal) do
    address = Map.get(individual, "residentialAddress", %{})

    %{
      "address" => Map.get(address, "street"),
      "streetNumber" => Map.get(address, "street2"),
      "city" => Map.get(address, "city", ""),
      "country" => Map.get(address, "country", ""),
      "zipCode" => Map.get(address, "postalCode", "")
    }
  end

  def legal_entity_to_promoter_dto(
        %{"type" => "soleProprietorship", "id" => _id, "soleProprietorship" => sole_proprietor_ship} = _legal
      ) do
    address = Map.get(sole_proprietor_ship, "registeredAddress", %{})

    %{
      "address" => Map.get(address, "street"),
      "streetNumber" => Map.get(address, "street2"),
      "city" => Map.get(address, "city", ""),
      "country" => Map.get(address, "country", ""),
      "zipCode" => Map.get(address, "postalCode", "")
    }
  end

  def legal_entity_to_promoter_dto(%{"type" => "organization", "id" => _id, "organization" => organization} = _legal) do
    address = Map.get(organization, "registeredAddress", %{})

    %{
      "address" => Map.get(address, "street"),
      "streetNumber" => Map.get(address, "street2"),
      "city" => Map.get(address, "city", ""),
      "country" => Map.get(address, "country", ""),
      "zipCode" => Map.get(address, "postalCode", "")
    }
  end

  # send promoter a mail with the onboarding link for registration completion
  def send_promoter_onboarding_link(_link) do
    # Todo implement with the email service
  end

  # get the onboarding link
  # Temporary hack to fix Adyen case 04516833
  # DEV testing promoter
  @parry_promo_id "d3ebeb32-c4d8-4d16-8c77-68bb998265e8"
  # PROD World of dance promoter
  @world_of_dance_id "ab931f65-c9c7-4481-8d12-5ef3b2c4c549"
  def get_promoter_on_boarding_link(%Promoter{id: promoter_id, legal_entity_id: legal_entity_id} = _promoter)
      when promoter_id in [@parry_promo_id, @world_of_dance_id] do
    LegalEntityManagement.create_hosted_onboarding_link(legal_entity_id, %{
      theme_id: "ONBT422JV223222P5HTF9CN9VF3XQV",
      return_url: Application.get_env(:events_service, :frontend_url),
      settings: %{
        "allowIntraRegionCrossBorderPayout" => true,
        "allowBankAccountFormatSelection" => true
      }
    })
  end

  # get the onboarding link
  def get_promoter_on_boarding_link(%Promoter{legal_entity_id: legal_entity_id} = _promoter) do
    LegalEntityManagement.create_hosted_onboarding_link(legal_entity_id, %{
      theme_id: "ONBT422JV223222P5HTF9CN9VF3XQV",
      return_url: Application.get_env(:events_service, :frontend_url),
      settings: %{
        "changeLegalEntityType" => false
      }
    })
  end

  def create_account_holder(promoter, legal_entity) do
    BalancePlatform.create_account_holder(%{
      legal_entity_id: legal_entity["id"],
      balance_platform: "EcostagGmbHPlatform",
      reference: promoter.id
    })
  end

  def create_balance_account(promoter, account_holder) do
    BalancePlatform.create_balance_account(%{
      account_holder_id: account_holder["id"],
      reference: promoter.id,
      description: ""
    })
  end

  def create_promoter_legal_entity(%Promoter{entity_type: :individual, display_name: _display_name} = promoter, country) do
    params = %{country: country, first_name: promoter.given_name, last_name: promoter.family_name}
    LegalEntityManagement.create_legal_entity(params, :individual)
  end

  def create_promoter_legal_entity(
        %Promoter{entity_type: :soleProprietorship, company_name: company_name} = promoter,
        country
      ) do
    sole_proprietor_payload = %{company_name: company_name, country: country}

    individual_payload = %{
      country: country,
      first_name: promoter.given_name,
      last_name: promoter.family_name
    }

    with {:ok, %{"id" => sole_legal_entity_id} = _sole_legal_entity} <-
           LegalEntityManagement.create_legal_entity(
             sole_proprietor_payload,
             :sole_proprietorship
           ),
         params = Map.put(individual_payload, :legal_entity, sole_legal_entity_id),
         {:ok, legal_entity} <- LegalEntityManagement.create_sole_proprietor_legal_entity(params) do
      {:ok, legal_entity}
    else
      {:error, _error} ->
        {:error, %{"msg" => "Could not create legal entity"}}
    end
  end

  def create_promoter_legal_entity(%Promoter{entity_type: :organization, company_name: company_name}, country) do
    LegalEntityManagement.create_legal_entity(%{legal_name: company_name, country: country}, :organization)
  end

  @spec validate_small_business(map()) :: boolean
  def validate_small_business(params) do
    case Map.get(params, "isSmallBusiness") do
      # FXME: This is a temporary fix for the case where the isSmallBusiness is not sent by the frontend
      nil -> true
      false -> true
      _ -> do_validate_small_business(params)
    end
  end

  def do_validate_small_business(%{"isSmallBusiness" => true, "country" => "DE", "entityType" => entity_type})
      when entity_type in ["soleProprietorship", "organization"],
      do: true

  def do_validate_small_business(_params), do: false

  def create_promoter_create_param(%{"entityType" => "individual"} = promoter_param, %{"id" => user_id} = _userinfo) do
    %{
      "display_name" => promoter_param["displayName"],
      "given_name" => promoter_param["firstName"],
      "family_name" => promoter_param["lastName"],
      "company_name" => promoter_param["firstName"] <> " " <> promoter_param["lastName"],
      "tax_id" => promoter_param["taxId"],
      "entity_type" => promoter_param["entityType"],
      "country" => promoter_param["country"],
      "created_by_document_id" => user_id,
      "is_small_business" => false
    }
  end

  def create_promoter_create_param(
        %{"entityType" => "soleProprietorship"} = promoter_param,
        %{"id" => user_id} = _userinfo
      ) do
    %{
      "display_name" => promoter_param["displayName"],
      "given_name" => promoter_param["firstName"],
      "family_name" => promoter_param["lastName"],
      "company_name" => promoter_param["companyName"],
      "vat_id" => promoter_param["vatId"],
      "tax_id" => promoter_param["taxId"],
      "owner_id" => user_id,
      "entity_type" => promoter_param["entityType"],
      "country" => promoter_param["country"],
      "created_by_document_id" => user_id,
      "is_small_business" => promoter_param["isSmallBusiness"] || false
    }
  end

  def create_promoter_create_param(%{"entityType" => "organization"} = promoter_param, %{"id" => user_id} = _userinfo) do
    %{
      "display_name" => promoter_param["displayName"],
      "company_name" => promoter_param["companyName"],
      "vat_id" => promoter_param["vatId"],
      "tax_id" => promoter_param["taxId"],
      "owner_id" => user_id,
      "entity_type" => promoter_param["entityType"],
      "country" => promoter_param["country"],
      "created_by_document_id" => user_id,
      "is_small_business" => promoter_param["isSmallBusiness"] || false
    }
  end

  @doc """
  Updates a promoter.

  ## Examples

      iex> update_promoter(promoter, %{field: new_value})
      {:ok, %Promoter{}}

      iex> update_promoter(promoter, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_promoter(%Promoter{} = promoter, attrs) do
    promoter
    |> Promoter.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a promoter.

  ## Examples

      iex> delete_promoter(promoter)
      {:ok, %Promoter{}}

      iex> delete_promoter(promoter)
      {:error, %Ecto.Changeset{}}

  """
  def delete_promoter(%Promoter{} = promoter) do
    Repo.delete(promoter)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking promoter changes.

  ## Examples

      iex> change_promoter(promoter)
      %Ecto.Changeset{data: %Promoter{}}

  """
  def change_promoter(%Promoter{} = promoter, attrs \\ %{}) do
    Promoter.changeset(promoter, attrs)
  end

  @doc """
  Returns the list of promoter_employees.

  ## Examples

      iex> list_promoter_employees()
      [%PromoterEmployee{}, ...]

  """
  def list_promoter_employees do
    Repo.all(PromoterEmployee)
  end

  @doc """
  Gets a single promoter_employee.

  Raises `Ecto.NoResultsError` if the Promoter employee does not exist.

  ## Examples

      iex> get_promoter_employee!(123)
      %PromoterEmployee{}

      iex> get_promoter_employee!(456)
      ** (Ecto.NoResultsError)

  """
  def get_promoter_employee!(id), do: Repo.get!(PromoterEmployee, id)

  @doc """
  Creates a promoter_employee.

  ## Examples

      iex> create_promoter_employee(%{field: value})
      {:ok, %PromoterEmployee{}}

      iex> create_promoter_employee(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_promoter_employee(attrs \\ %{}) do
    %PromoterEmployee{}
    |> PromoterEmployee.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a promoter_employee.

  ## Examples

      iex> update_promoter_employee(promoter_employee, %{field: new_value})
      {:ok, %PromoterEmployee{}}

      iex> update_promoter_employee(promoter_employee, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_promoter_employee(%PromoterEmployee{} = promoter_employee, attrs) do
    promoter_employee
    |> PromoterEmployee.changeset(attrs)
    |> Repo.update()
  end

  def find_by_assigned_user(user_id) do
    query =
      from(pu in PromoterUser,
        where: pu.user_document_id == ^user_id,
        preload: [promoter: [address: [:country]]]
      )

    Repo.all(query)
  end

  @doc """
  Deletes a promoter_employee.

  ## Examples

      iex> delete_promoter_employee(promoter_employee)
      {:ok, %PromoterEmployee{}}

      iex> delete_promoter_employee(promoter_employee)
      {:error, %Ecto.Changeset{}}

  """
  def delete_promoter_employee(%PromoterEmployee{} = promoter_employee) do
    Repo.delete(promoter_employee)
  end

  def maybe_create_store_link(%Promoter{store_url: nil} = promoter) do
    params = %{"promoterId" => promoter.id, "id" => CouponCode.generate(parts: 1)}
    target_url = "#{Application.get_env(:events_service, :frontend_url)}/?#{URI.encode_query(params)}"

    if_result =
      if Unleash.enabled?(:use_shortlink_service) do
        Link.create(%{targetUrl: target_url})
      else
        FirebaseClient.shorten_link(target_url)
      end

    if_result
    |> case do
      {:ok, short_url} -> short_url
      {:error, _reason} -> target_url
      :not_found -> target_url
    end
    |> then(&update_promoter(promoter, %{"store_url" => &1}))
  end

  def maybe_create_store_link(%Promoter{store_url: _store_url} = promoter), do: {:ok, promoter}

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking promoter_employee changes.

  ## Examples

      iex> change_promoter_employee(promoter_employee)
      %Ecto.Changeset{data: %PromoterEmployee{}}

  """
  def change_promoter_employee(%PromoterEmployee{} = promoter_employee, attrs \\ %{}) do
    PromoterEmployee.changeset(promoter_employee, attrs)
  end

  def get_promoter_insights_dashboard_link(mobile, dashboard_type, event_id, promoter_id, is_event_id) do
    BackendV2.get_looker_dashboard(%{
      mobile: mobile,
      dashboardType: dashboard_type,
      isEventId: is_event_id,
      eventId: event_id,
      promoterId: promoter_id
    })
  end

  def insert_promoter_creation_mail_job(email, link) do
    %{email: email, link: link}
    |> PromoterEmailWorker.build()
    |> Oban.insert!()
  end

  def check_complete_address_promoter(nil), do: false

  def check_complete_address_promoter(%{country: nil} = _address), do: false

  def check_complete_address_promoter(address) do
    true
    |> validate_string_is_not_empty(address.street_address)
    |> validate_string_is_not_empty(address.locality)
    |> validate_string_is_not_empty(address.postal_code)
  end

  def check_complete_address_adyen(nil), do: false

  def check_complete_address_adyen(legal_entity) do
    true
    |> validate_string_is_not_empty(legal_entity["address"])
    |> validate_string_is_not_empty(legal_entity["city"])
    |> validate_string_is_not_empty(legal_entity["country"])
    |> validate_string_is_not_empty(legal_entity["zipCode"])
  end

  def check_onboarding_completed(nil), do: false

  def check_onboarding_completed(
        %{"capabilities" => %{"sendToTransferInstrument" => %{"verificationStatus" => "valid"}}} = _legal_entity
      ),
      do: true

  def check_onboarding_completed(_legal_entity), do: false

  @spec get_promoter_by_event_id(event_id :: Ecto.UUID.t()) :: map() | nil
  def get_promoter_by_event_id(event_id, preloads \\ []) do
    query =
      from(p in Promoter,
        join: event in Event,
        on: event.promoter_id == p.id,
        where: event.id == ^event_id,
        preload: ^preloads
      )

    Repo.one(query)
  end

  defp validate_promoter_address(%Promoter{address: address} = promoter) do
    if check_complete_address_promoter(address) do
      :ok
    else
      validate_adyen_legal_entity(promoter)
    end
  end

  defp validate_adyen_legal_entity(%Promoter{legal_entity_id: legal_entity_id} = promoter) do
    with {:ok, legal_entity} <- get_legal_entity(legal_entity_id),
         legal_entity_dto = legal_entity_to_promoter_dto(legal_entity),
         true <- check_complete_address_adyen(legal_entity_dto) && check_onboarding_completed(legal_entity) do
      create_promoter_address(legal_entity_dto, promoter)
      :ok
    else
      _ -> {:error, :invalid}
    end
  end

  defp validate_string_is_not_empty(_bool, nil), do: false
  defp validate_string_is_not_empty(true, string), do: String.length(string) > 0
  defp validate_string_is_not_empty(false, _string), do: false

  defp create_organizer_seller(promoter) do
    case Sellers.create_organizer_seller(promoter) do
      {:ok, seller} -> {:ok, seller}
      {:error, :user_not_found} -> {:error, :user_not_found}
      {:error, :seller, changeset, _} -> {:error, changeset}
      {:error, _operation, changeset} -> {:error, changeset}
    end
  end
end
