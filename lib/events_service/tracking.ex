defmodule EventsService.Tracking do
  @moduledoc """
  The Tracking context.
  """

  use EventsService.GoogleTrackingClient

  import Ecto.Query, warn: false

  alias Ecto.Multi
  alias EventsService.Promotion.CouponCode
  alias EventsService.Repo
  alias EventsService.Tracking.TrackingLink
  alias EventsService.Tracking.TrackingLinksCount
  alias EventsService.Tracking.TrackingPixel
  alias EventsService.Tracking.TrackingPixelCredential
  alias EventsService.Util.FirebaseClient
  alias ExServiceClient.Services.ShortlinkService.Link

  require Logger

  @doc """
  Returns the list of tracking_pixels.

  ## Examples

      iex> list_tracking_pixels()
      [%TrackingPixel{}, ...]

  """
  def list_tracking_pixels(event_id, preloads \\ [:tracking_pixel_credentials]) do
    query =
      from(tp in TrackingPixel,
        where: tp.event_id == ^event_id,
        preload: ^preloads
      )

    Repo.all(query)
  end

  @doc """
  Gets a single tracking_pixel.

  Raises `Ecto.NoResultsError` if the Tracking pixel does not exist.

  ## Examples

      iex> get_tracking_pixel!(123)
      %TrackingPixel{}

      iex> get_tracking_pixel!(456)
      ** (Ecto.NoResultsError)

  """
  def get_tracking_pixel!(id) do
    TrackingPixel
    |> Repo.get!(id)
    |> Repo.preload([:tracking_pixel_credentials])
  end

  @doc """
  Creates a tracking_pixel.

  ## Examples

      iex> create_tracking_pixel(%{field: value})
      {:ok, %TrackingPixel{}}

      iex> create_tracking_pixel(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_tracking_pixel(attrs \\ %{}) do
    result =
      Multi.new()
      |> Multi.insert(:tracking_pixel, TrackingPixel.changeset(%TrackingPixel{}, attrs))
      |> insert_credentials(attrs["trackingPixelCredentials"])
      |> Repo.transaction()

    case result do
      {:ok, %{tracking_pixel: tracking_pixel}} ->
        {:ok, tracking_pixel}

      {:error, failed_operation, failed_value, _changes_so_far} ->
        Logger.error(
          "Failed to create tracking pixel in step #{inspect(failed_operation)} and error #{inspect(failed_value)}"
        )

        {:error, failed_value}
    end
  end

  defp insert_credentials(multi, nil), do: multi

  defp insert_credentials(multi, credentials) do
    credentials
    |> prepare_credentials()
    |> Enum.reduce({multi, 0}, fn credential, {multi, count} ->
      multi
      |> Multi.insert({:credential, count}, fn %{tracking_pixel: tracking_pixel} ->
        TrackingPixelCredential.changeset(
          %TrackingPixelCredential{},
          Map.put(credential, "tracking_pixel_id", tracking_pixel.id)
        )
      end)
      |> then(&{&1, count + 1})
    end)
    |> elem(0)
  end

  defp prepare_credentials(credentials) do
    Enum.map(credentials, fn item ->
      %{"type" => String.to_atom(item["type"]), "value" => item["value"]}
    end)
  end

  @doc """
  Updates a tracking_pixel.

  ## Examples

      iex> update_tracking_pixel(tracking_pixel, %{field: new_value})
      {:ok, %TrackingPixel{}}

      iex> update_tracking_pixel(tracking_pixel, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_tracking_pixel(%TrackingPixel{} = tracking_pixel, attrs) do
    query =
      from(tpc in TrackingPixelCredential, where: tpc.tracking_pixel_id == ^tracking_pixel.id)

    result =
      Multi.new()
      |> Multi.update(:tracking_pixel, TrackingPixel.changeset(tracking_pixel, attrs))
      |> Multi.delete_all(:credentials_delete, query)
      |> insert_credentials(attrs["trackingPixelCredentials"])
      |> Repo.transaction()

    case result do
      {:ok, %{tracking_pixel: tracking_pixel}} ->
        {:ok, tracking_pixel}

      {:error, failed_operation, failed_value, _changes_so_far} ->
        Logger.error(
          "Failed to update tracking pixel in step #{inspect(failed_operation)} and error #{inspect(failed_value)}"
        )

        {:error, failed_value}
    end
  end

  @doc """
  Deletes a tracking_pixel.

  ## Examples

      iex> delete_tracking_pixel(tracking_pixel)
      {:ok, %TrackingPixel{}}

      iex> delete_tracking_pixel(tracking_pixel)
      {:error, %Ecto.Changeset{}}

  """
  def delete_tracking_pixel(%TrackingPixel{} = tracking_pixel) do
    now = DateTime.utc_now()

    result =
      Multi.new()
      |> Multi.update(
        :tracking_pixel,
        TrackingPixel.changeset(tracking_pixel, %{"deleted_at" => now})
      )
      |> Multi.update_all(
        :credentials,
        fn %{tracking_pixel: tracking_pixel} ->
          from(tpc in TrackingPixelCredential,
            where: tpc.tracking_pixel_id == ^tracking_pixel.id,
            update: [set: [deleted_at: ^now]]
          )
        end,
        []
      )
      |> Repo.transaction()

    case result do
      {:ok, _} ->
        {:ok, %TrackingPixel{}}

      {:error, failed_operation, failed_value, _changes_so_far} ->
        Logger.error(
          "Failed to delete tracking pixel in step #{inspect(failed_operation)} and error #{inspect(failed_value)}"
        )

        {:error, failed_value}
    end
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking tracking_pixel changes.

  ## Examples

      iex> change_tracking_pixel(tracking_pixel)
      %Ecto.Changeset{data: %TrackingPixel{}}

  """
  def change_tracking_pixel(%TrackingPixel{} = tracking_pixel, attrs \\ %{}) do
    TrackingPixel.changeset(tracking_pixel, attrs)
  end

  @doc """
  Returns the list of tracking_links.

  ## Examples

      iex> list_tracking_links()
      [%TrackingLink{}, ...]

  """
  def list_tracking_links do
    query = from(tl in TrackingLink, where: is_nil(tl.deleted_at))
    Repo.all(query)
  end

  @doc """
  Gets a single tracking_link.

  Raises `Ecto.NoResultsError` if the Tracking link does not exist.

  ## Examples

      iex> get_tracking_link!(123)
      %TrackingLink{}

      iex> get_tracking_link!(456)
      ** (Ecto.NoResultsError)

  """
  def get_tracking_link!(id), do: Repo.get!(TrackingLink, id)

  @doc """
  Creates a tracking_link.

  ## Examples

      iex> create_tracking_link(%{field: value})
      {:ok, %TrackingLink{}}

      iex> create_tracking_link(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_tracking_link(attrs \\ %{}) do
    %TrackingLink{}
    |> TrackingLink.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a tracking_link.

  ## Examples

      iex> update_tracking_link(tracking_link, %{field: new_value})
      {:ok, %TrackingLink{}}

      iex> update_tracking_link(tracking_link, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_tracking_link(%TrackingLink{} = tracking_link, attrs) do
    tracking_link
    |> TrackingLink.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a tracking_link.

  ## Examples

      iex> delete_tracking_link(tracking_link)
      {:ok, %TrackingLink{}}

      iex> delete_tracking_link(tracking_link)
      {:error, %Ecto.Changeset{}}

  """
  def delete_tracking_link(%TrackingLink{} = tracking_link) do
    Repo.delete(tracking_link)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking tracking_link changes.

  ## Examples

      iex> change_tracking_link(tracking_link)
      %Ecto.Changeset{data: %TrackingLink{}}

  """
  def change_tracking_link(%TrackingLink{} = tracking_link, attrs \\ %{}) do
    TrackingLink.changeset(tracking_link, attrs)
  end

  def maybe_create_tracking_link(event_id) do
    params = %{"id" => CouponCode.generate(parts: 1)}
    # Fixme: Add revert to `events` asap
    target_url =
      "#{Application.get_env(:events_service, :frontend_url)}/events/#{event_id}/?#{URI.encode_query(params)}"

    if Unleash.enabled?(:use_shortlink_service) do
      {:ok, link} = Link.create(%{targetUrl: target_url})
      {:ok, %{"target_url" => target_url, "url" => link.srcUrl, "event_id" => event_id}}
    else
      {:ok, short_link} = FirebaseClient.shorten_link(target_url)
      {:ok, %{"target_url" => target_url, "url" => short_link, "event_id" => event_id}}
    end
  end

  def execute_tracking_link_sync do
    Logger.debug("Starting tracking link synchronization from Firestore to BigQuery")
    tracking_links = list_tracking_links()
    Logger.debug("Found #{length(tracking_links)} tracking link(s) to process #{inspect(tracking_links)}")

    try do
      process_links(tracking_links)
    rescue
      exception -> Logger.error("Failed to process tracking links: #{inspect(exception)}")
    end
  end

  def process_links(tracking_links) do
    Logger.debug("Starting to process tracking link(s)")
    {:ok, token} = get_tracking_service_client_token()

    processed_links =
      tracking_links
      |> Task.async_stream(fn item -> process_link(item, token) end, ordered: false)
      |> Enum.flat_map(fn {:ok, item} -> [item] end)

    Logger.debug("Processed tracking link(s) #{inspect(processed_links)}")
    merge_dynamic_link_data(processed_links)
    Logger.debug("Finished processing tracking link(s)")
    :ok
  end

  @doc """
  Process a tracking link.
  Attentions: Firebase only keeps the data for 30 days. So, we need to process the data daily.
  In case we need to process the data for a back-fill( 30 days max period), we need to change the durationDays parameter to 30.
  Per default, we process the data for the last day.
  """
  def process_link(tracking_link, token) do
    Logger.debug("Processing tracking link #{inspect(tracking_link)}")
    encode_url = encode_tracking_link_url(tracking_link.url)

    [authorization: token.token]
    |> client()
    |> Tesla.get("/#{encode_url}/linkStats?durationDays=1")
    |> parse_response()
    |> count_link_click()
    |> create_big_query_entry(tracking_link)
  end

  def create_big_query_entry(count, tracking_link) do
    Logger.debug("Creating BigQuery entry for tracking link #{inspect(tracking_link)} with count #{inspect(count)}")

    %{
      id: Ecto.UUID.generate(),
      url: tracking_link.url,
      target_url: tracking_link.target_url,
      click_count: count,
      inserted_at: DateTime.truncate(DateTime.utc_now(), :second)
    }
  end

  defp encode_tracking_link_url(url) do
    url |> Plug.Conn.Query.encode() |> String.replace("=", "")
  end

  defp count_link_click({:ok, %{"linkEventStats" => stats}}) when is_list(stats),
    do: Enum.reduce(stats, 0, fn %{"count" => count}, acc -> acc + String.to_integer(count) end)

  defp count_link_click({:ok, _}), do: 0
  defp count_link_click(_), do: 0

  def get_tracking_service_client_token do
    scopes = ["https://www.googleapis.com/auth/firebase"]
    service_account = Application.get_env(:events_service, :service_account)

    credentials = Jason.decode!(service_account)

    Goth.Token.fetch(source: {:service_account, credentials, scopes: scopes})
  end

  def merge_dynamic_link_data(data_list) do
    Logger.debug("Merging dynamic link data #{inspect(data_list)}")

    Multi.new()
    |> Multi.insert_all(:insert_all_counts, TrackingLinksCount, data_list)
    |> Repo.transaction()
    |> case do
      {:ok, _} ->
        Logger.debug("Merged dynamic link data successfully")

      {:error, error} ->
        Logger.error("Failed to merge dynamic link data: #{inspect(error)}")
    end
  end
end
