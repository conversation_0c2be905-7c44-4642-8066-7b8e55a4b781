defmodule EventsService.VariantQuota do
  @moduledoc """
  Utility module for calculating variant quota information.

  This module provides functions to calculate different types of quota for variants
  based on their distribution type, status, and associated counters.
  """

  alias EventsService.Events.Variant

  @doc """
  Calculates the base quota for a variant based on its distribution type.

  ## Parameters

    * `variant` - A variant struct containing distribution_type and related data

  ## Returns

    * `integer()` - The calculated quota for the variant
    * `nil` - If quota cannot be determined

  ## Examples

      iex> variant = %{distribution_type: :REGULAR, ticket_category: %{quota: 100, reserved_quota: 20}}
      iex> EventsService.VariantQuota.get_variant_quota(variant)
      80

      iex> variant = %{distribution_type: :GUEST_LIST_INVITATION}
      iex> EventsService.VariantQuota.get_variant_quota(variant)
      nil

  ## Distribution Types

    * `:REGULAR` - Uses ticket category quota minus reserved quota
    * `:SALES_CHANNEL` - Uses sales channel amount of objects
    * `:GUEST_LIST_INVITATION` - Returns nil (quota not applicable)
  """
  @spec get_variant_quota(Variant.t() | map()) :: integer() | nil
  def get_variant_quota(%{
        distribution_type: :REGULAR,
        ticket_category: %{quota: quota, reserved_quota: reserved_quota}
      }),
      do: quota - reserved_quota

  def get_variant_quota(%{
        distribution_type: :SALES_CHANNEL,
        sales_channel: %{channel_config: %{amount_of_objects: quota}}
      }),
      do: quota

  # Currently one variant is used in more than one guest_list_invitation, so it's not clear to say what is the quota.
  # The distribution_state is not used in Code, it's only a fallback for 'broken' database entries.
  # https://ecostag.atlassian.net/browse/SD1-3993
  def get_variant_quota(%{distribution_type: :GUEST_LIST_INVITATION}), do: nil

  def get_variant_quota(_variant), do: nil

  @doc """
  Calculates the remaining quota for a variant by subtracting sold items from the base quota.

  This function uses the variant's quota and variant_counter to determine how many
  items are still available for sale.

  ## Parameters

    * `variant` - A variant struct containing quota and variant_counter data

  ## Returns

    * `integer()` - The remaining quota (quota - sold)
    * `nil` - If quota is nil or cannot be determined

  ## Examples

      iex> variant = %{quota: 100, variant_counter: %{sold: 30}}
      iex> EventsService.VariantQuota.get_remaining_quota(variant)
      70

      iex> variant = %{quota: nil}
      iex> EventsService.VariantQuota.get_remaining_quota(variant)
      nil

      iex> variant = %{quota: 50, variant_counter: nil}
      iex> EventsService.VariantQuota.get_remaining_quota(variant)
      50
  """
  @spec get_remaining_quota(Variant.t() | map()) :: integer() | nil
  def get_remaining_quota(%{quota: nil} = _variant), do: nil
  def get_remaining_quota(%{quota: quota, variant_counter: nil} = _variant), do: quota
  def get_remaining_quota(%{quota: quota, variant_counter: %{sold: sold}}), do: quota - sold
  def get_remaining_quota(_variant), do: nil

  @doc """
  Calculates the publicly visible remaining quota for active variants.

  This function provides quota information that should be displayed to the public,
  taking into account the variant's status, distribution type, and various counters.
  It handles different distribution types and counter scenarios.

  ## Parameters

    * `variant` - A variant struct containing status, distribution_type, and counter data

  ## Returns

    * `integer()` - The public remaining quota (always >= 0)
    * `nil` - If quota should not be displayed publicly

  ## Examples

      iex> variant = %{status: :ACTIVE, distribution_type: :GUEST_LIST_INVITATION}
      iex> EventsService.VariantQuota.get_public_remaining_quota(variant)
      nil

      iex> variant = %{
      ...>   status: :ACTIVE,
      ...>   distribution_type: :REGULAR,
      ...>   ticket_category: %{ticket_category_counter: nil},
      ...>   quota: 100
      ...> }
      iex> EventsService.VariantQuota.get_public_remaining_quota(variant)
      100

  ## Behavior by Distribution Type

    * `:GUEST_LIST_INVITATION` - Always returns nil for active variants
    * `:REGULAR` - Uses ticket category counters when available, falls back to variant quota
    * `:SALES_CHANNEL` - Uses variant quota minus existing items
    * Other cases - Uses quota minus sold items, with minimum of 0
  """
  @spec get_public_remaining_quota(Variant.t() | map()) :: integer() | nil
  def get_public_remaining_quota(%{status: :ACTIVE, distribution_type: :GUEST_LIST_INVITATION} = _variant), do: nil

  def get_public_remaining_quota(
        %{status: :ACTIVE, distribution_type: :REGULAR, ticket_category: %{ticket_category_counter: nil}} = variant
      ),
      do: get_variant_quota(variant)

  def get_public_remaining_quota(
        %{
          status: :ACTIVE,
          distribution_type: :REGULAR,
          ticket_category: %{
            ticket_category_counter: %{existing_items: existing_items, existing_reserved_items: existing_reserved_items}
          }
        } = variant
      ),
      do: (get_variant_quota(variant) || 0) - existing_items + existing_reserved_items

  def get_public_remaining_quota(
        %{status: :ACTIVE, distribution_type: :SALES_CHANNEL, variant_counter: %{existing_items: existing_items}} =
          variant
      ),
      do: (get_variant_quota(variant) || 0) - existing_items

  def get_public_remaining_quota(%{status: :ACTIVE, quota: quota, variant_counter: %{sold: sold}}) do
    max(0, quota - sold)
  end

  def get_public_remaining_quota(_variant), do: nil
end
