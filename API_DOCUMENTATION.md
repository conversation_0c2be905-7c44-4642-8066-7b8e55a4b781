# Shortlink Service API Documentation

## Overview

The Shortlink Service provides a modernized REST API for creating and managing short URLs. The service features a layered architecture with comprehensive validation, structured error handling, and state-of-the-art Go practices.

## Architecture

The service follows a clean architecture pattern with the following layers:

- **Handler Layer**: HTTP request/response handling
- **Service Layer**: Business logic and validation
- **Repository Layer**: Data access and persistence
- **Model Layer**: Domain models and DTOs

## Authentication

All API endpoints require authentication via API key passed in the `X-Api-Key` header.

```bash
curl -H "X-Api-Key: your-api-key-here" ...
```

## Endpoints

### Create Short Link

Creates a new short link with optional custom short URL.

**Endpoint:** `POST /links`

**Request Headers:**
- `Content-Type: application/json`
- `X-Api-Key: {your-api-key}`

**Request Body:**
```json
{
  "targetUrl": "https://example.com/some/long/path",
  "shortlink": "https://short.ly/custom" // optional
}
```

**Request Fields:**
- `targetUrl` (required): The destination URL to redirect to. Must be a valid HTTP/HTTPS URL.
- `shortlink` (optional): Custom short URL. If not provided, a random one will be generated.

**Success Response (201 Created):**
```json
{
  "id": "123e4567-e89b-12d3-a456-426614174000",
  "shortlink": "https://short.ly/abc123",
  "targetUrl": "https://example.com/some/long/path",
  "insertedAt": "2023-01-01T12:00:00Z",
  "updatedAt": "2023-01-01T12:00:00Z"
}
```

**Error Responses:**

All error responses follow a consistent structure:

```json
{
  "errorCode": "ERROR_TYPE",
  "message": "Human readable error message",
  "data": [
    {
      "field": "fieldName",
      "message": "Field-specific error message",
      "value": "invalid-value"
    }
  ]
}
```

**Error Codes:**

- `VALIDATION_ERROR` (400): Request validation failed
- `UNAUTHORIZED` (401): Invalid or missing API key
- `DUPLICATE_LINK` (409): Short URL already exists
- `INVALID_URL` (400): Malformed URL provided
- `INTERNAL_ERROR` (500): Server error

**Example Requests:**

1. **Create link with auto-generated short URL:**
```bash
curl -X POST https://api.example.com/links \
  -H "Content-Type: application/json" \
  -H "X-Api-Key: your-api-key" \
  -d '{
    "targetUrl": "https://example.com/very/long/path/to/resource"
  }'
```

2. **Create link with custom short URL:**
```bash
curl -X POST https://api.example.com/links \
  -H "Content-Type: application/json" \
  -H "X-Api-Key: your-api-key" \
  -d '{
    "targetUrl": "https://example.com/resource",
    "shortlink": "https://short.ly/my-custom-link"
  }'
```

**Example Error Responses:**

1. **Validation Error:**
```json
{
  "errorCode": "VALIDATION_ERROR",
  "message": "Request validation failed",
  "data": [
    {
      "field": "targetUrl",
      "message": "targetUrl is required",
      "value": ""
    }
  ]
}
```

2. **Invalid URL Error:**
```json
{
  "errorCode": "VALIDATION_ERROR",
  "message": "Request validation failed",
  "data": [
    {
      "field": "targetUrl",
      "message": "targetUrl must be a valid URL",
      "value": "not-a-valid-url"
    }
  ]
}
```

3. **Duplicate Link Error:**
```json
{
  "errorCode": "DUPLICATE_LINK",
  "message": "A link with this short URL already exists",
  "data": []
}
```

4. **Unauthorized Error:**
```json
{
  "errorCode": "UNAUTHORIZED",
  "message": "Invalid or missing API key",
  "data": []
}
```

## Validation Rules

### Target URL
- **Required**: Yes
- **Format**: Must be a valid HTTP or HTTPS URL
- **Examples**: 
  - ✅ `https://example.com`
  - ✅ `http://example.com/path?param=value`
  - ❌ `example.com` (missing scheme)
  - ❌ `ftp://example.com` (invalid scheme)

### Short Link (Optional)
- **Required**: No
- **Format**: Must be a valid HTTP or HTTPS URL if provided
- **Behavior**: If not provided, a random short URL will be generated
- **Examples**:
  - ✅ `https://short.ly/custom`
  - ✅ `http://mysite.com/abc123`
  - ❌ `short.ly/custom` (missing scheme)

## Rate Limiting

Currently, there are no explicit rate limits, but this may be added in future versions.

## Changelog

### Version 2.0 (Current)
- Modernized architecture with layered design
- Comprehensive input validation
- Structured error responses
- Improved API documentation
- Added comprehensive test coverage

### Version 1.0 (Legacy)
- Basic link creation functionality
- Simple error handling
- Direct database access from handlers
