package migrations

import "gofr.dev/pkg/gofr/migration"

const createTable = `CREATE TABLE IF NOT EXISTS shortlink.link
(
	id             UUID            NOT NULL DEFAULT gen_random_uuid()
	PRIMARY KEY,
    type           varchar(16) NOT NULL,
    path           varchar(48)  NOT NULL,
    original_url   varchar(2000) NOT NULL
);`

func createTableLink() migration.Migrate {
	return migration.Migrate{
		UP: func(d migration.Datasource) error {
			_, err := d.SQL.Exec(createTable)
			if err != nil {
				return err
			}
			return nil
		},
	}
}
