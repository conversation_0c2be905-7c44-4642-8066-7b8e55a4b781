defmodule OrdersService.Repo.Migrations.CreateSettledPayments do
  use Ecto.Migration

  def change do
    create_if_not_exists table(:settled_payments, primary_key: false) do
      add :id, :binary_id, primary_key: true
      add :company_account, :string
      add :merchant_account, :string
      add :psp_reference, :string, null: true
      add :merchant_reference, :uuid, null: true
      add :payment_method, :string, null: true
      add :creation_date, :utc_datetime
      add :timezone, :string
      add :type, :string
      add :modification_references, :string
      add :gross_currency, :string, null: true
      add :gross_debit, :float, null: true
      add :gross_credit, :float, null: true
      add :exchange_rate, :float, null: true
      add :net_currency, :string
      add :net_debit, :float, null: true
      add :net_credit, :float, null: true
      add :commission, :float, null: true
      add :markup, :float, null: true
      add :scheme_fees, :float, null: true
      add :interchange, :float, null: true
      add :payment_method_variant, :string, null: true
      add :modification_merchant_reference, :string, null: true
      add :batch_number, :integer
      timestamps()
    end

    create_if_not_exists index(:settled_payments, [:psp_reference])
    create_if_not_exists index(:settled_payments, [:merchant_reference])
  end
end
