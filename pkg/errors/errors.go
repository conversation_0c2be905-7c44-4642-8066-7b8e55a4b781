package errors

import (
	"fmt"
	"net/http"

	"github.com/stagedates/shortlink-service/pkg/models"
)

type AppError struct {
	Code       string                   `json:"code"`
	Message    string                   `json:"message"`
	Data       []models.ValidationError `json:"data,omitempty"`
	HTTPStatus int                      `json:"-"`
	Cause      error                    `json:"-"`
}

func (e *AppError) Error() string {
	if e.Cause != nil {
		return fmt.Sprintf("%s: %s (caused by: %v)", e.Code, e.Message, e.Cause)
	}
	return fmt.Sprintf("%s: %s", e.Code, e.Message)
}

func (e *AppError) StatusCode() int {
	return e.HTTPStatus
}

func (e *AppError) Response() map[string]any {
	response := map[string]any{
		"errorCode": e.Code,
		"message":   e.Message,
	}

	if len(e.Data) > 0 {
		response["data"] = e.Data
	}

	return response
}

func (e *AppError) ToErrorResponse() *models.ErrorResponse {
	return &models.ErrorResponse{
		ErrorCode: e.Code,
		Message:   e.Message,
		Data:      e.Data,
	}
}

func NewValidationError(message string, validationErrors []models.ValidationError) *AppError {
	return &AppError{
		Code:       models.ErrorCodeValidation,
		Message:    message,
		Data:       validationErrors,
		HTTPStatus: http.StatusBadRequest,
	}
}

func NewUnauthorizedError(message string) *AppError {
	return &AppError{
		Code:       models.ErrorCodeUnauthorized,
		Message:    message,
		HTTPStatus: http.StatusUnauthorized,
	}
}

func NewInternalError(message string, cause error) *AppError {
	return &AppError{
		Code:       models.ErrorCodeInternalError,
		Message:    message,
		HTTPStatus: http.StatusInternalServerError,
		Cause:      cause,
	}
}

func NewDuplicateLinkError(message string) *AppError {
	return &AppError{
		Code:       models.ErrorCodeDuplicateLink,
		Message:    message,
		HTTPStatus: http.StatusConflict,
	}
}

func NewInvalidURLError(message string) *AppError {
	return &AppError{
		Code:       models.ErrorCodeInvalidURL,
		Message:    message,
		HTTPStatus: http.StatusBadRequest,
	}
}

func NewNotFoundError(message string) *AppError {
	return &AppError{
		Code:       models.ErrorCodeNotFound,
		Message:    message,
		HTTPStatus: http.StatusNotFound,
	}
}

func IsAppError(err error) (*AppError, bool) {
	if appErr, ok := err.(*AppError); ok {
		return appErr, true
	}
	return nil, false
}

func WrapError(err error, code, message string, httpStatus int) *AppError {
	return &AppError{
		Code:       code,
		Message:    message,
		HTTPStatus: httpStatus,
		Cause:      err,
	}
}
