package errors

import (
	"net/http"
	"testing"

	"github.com/stagedates/shortlink-service/pkg/models"
)

func TestAppError_StatusCode(t *testing.T) {
	tests := []struct {
		name           string
		error          *AppError
		expectedStatus int
	}{
		{
			name:           "validation error",
			error:          NewValidationError("test", nil),
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "unauthorized error",
			error:          NewUnauthorizedError("test"),
			expectedStatus: http.StatusUnauthorized,
		},
		{
			name:           "internal error",
			error:          NewInternalError("test", nil),
			expectedStatus: http.StatusInternalServerError,
		},
		{
			name:           "duplicate link error",
			error:          NewDuplicateLinkError("test"),
			expectedStatus: http.StatusConflict,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.error.StatusCode(); got != tt.expectedStatus {
				t.<PERSON>rf("StatusCode() = %v, want %v", got, tt.expectedStatus)
			}
		})
	}
}

func TestAppError_Response(t *testing.T) {
	tests := []struct {
		name     string
		error    *AppError
		expected map[string]any
	}{
		{
			name:  "validation error without data",
			error: NewValidationError("Request validation failed", nil),
			expected: map[string]any{
				"errorCode": models.ErrorCodeValidation,
				"message":   "Request validation failed",
			},
		},
		{
			name: "validation error with data",
			error: NewValidationError("Request validation failed", []models.ValidationError{
				{
					Field:   "targetUrl",
					Message: "targetUrl is required",
					Value:   "",
				},
			}),
			expected: map[string]any{
				"errorCode": models.ErrorCodeValidation,
				"message":   "Request validation failed",
				"data": []models.ValidationError{
					{
						Field:   "targetUrl",
						Message: "targetUrl is required",
						Value:   "",
					},
				},
			},
		},
		{
			name:  "unauthorized error",
			error: NewUnauthorizedError("Invalid API key"),
			expected: map[string]any{
				"errorCode": models.ErrorCodeUnauthorized,
				"message":   "Invalid API key",
			},
		},
		{
			name:  "duplicate link error",
			error: NewDuplicateLinkError("Link already exists"),
			expected: map[string]any{
				"errorCode": models.ErrorCodeDuplicateLink,
				"message":   "Link already exists",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			response := tt.error.Response()

			// Check errorCode
			if response["errorCode"] != tt.expected["errorCode"] {
				t.Errorf("Response() errorCode = %v, want %v", response["errorCode"], tt.expected["errorCode"])
			}

			// Check message
			if response["message"] != tt.expected["message"] {
				t.Errorf("Response() message = %v, want %v", response["message"], tt.expected["message"])
			}

			// Check data field presence
			expectedData, hasExpectedData := tt.expected["data"]
			responseData, hasResponseData := response["data"]

			if hasExpectedData != hasResponseData {
				t.Errorf("Response() data presence mismatch: expected %v, got %v", hasExpectedData, hasResponseData)
			}

			if hasExpectedData && hasResponseData {
				// For simplicity, we'll just check that data is present
				// In a real test, you might want to do deep comparison
				if responseData == nil {
					t.Errorf("Response() data is nil, expected %v", expectedData)
				}
			}
		})
	}
}

func TestAppError_Error(t *testing.T) {
	tests := []struct {
		name     string
		error    *AppError
		expected string
	}{
		{
			name:     "error without cause",
			error:    NewValidationError("test message", nil),
			expected: "VALIDATION_ERROR: test message",
		},
		{
			name: "error with cause",
			error: &AppError{
				Code:    "TEST_ERROR",
				Message: "test message",
				Cause:   NewInternalError("underlying error", nil),
			},
			expected: "TEST_ERROR: test message (caused by: INTERNAL_ERROR: underlying error)",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.error.Error(); got != tt.expected {
				t.Errorf("Error() = %v, want %v", got, tt.expected)
			}
		})
	}
}
