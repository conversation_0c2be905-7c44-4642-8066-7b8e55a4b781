package validators

import (
	"fmt"
	"net/url"
	"strings"

	"github.com/stagedates/shortlink-service/pkg/models"
)

type LinkValidator struct{}

func NewLinkValidator() *LinkValidator {
	return &LinkValidator{}
}

func (v *LinkValidator) ValidateCreateLinkRequest(req *models.CreateLinkRequest) []models.ValidationError {
	var errors []models.ValidationError

	if strings.TrimSpace(req.TargetURL) == "" {
		errors = append(errors, models.ValidationError{
			Field:   "targetUrl",
			Message: "targetUrl is required",
			Value:   req.TargetURL,
		})
	} else if !v.isValidURL(req.TargetURL) {
		errors = append(errors, models.ValidationError{
			Field:   "targetUrl",
			Message: "targetUrl must be a valid URL",
			Value:   req.TargetURL,
		})
	}

	if req.SrcURL != "" && !v.isValidURL(req.SrcURL) {
		errors = append(errors, models.ValidationError{
			Field:   "shortlink",
			Message: "shortlink must be a valid URL if provided",
			Value:   req.SrcURL,
		})
	}

	return errors
}

func (v *LinkValidator) isValidURL(str string) bool {
	if str == "" {
		return false
	}

	parsedURL, err := url.Parse(str)
	if err != nil {
		return false
	}

	if parsedURL.Scheme == "" {
		return false
	}

	if parsedURL.Scheme != "http" && parsedURL.Scheme != "https" {
		return false
	}

	if parsedURL.Host == "" {
		return false
	}

	return true
}

func (v *LinkValidator) ValidateURL(fieldName, urlStr string) *models.ValidationError {
	if !v.isValidURL(urlStr) {
		return &models.ValidationError{
			Field:   fieldName,
			Message: fmt.Sprintf("%s must be a valid URL", fieldName),
			Value:   urlStr,
		}
	}
	return nil
}

func (v *LinkValidator) ValidateRequired(fieldName, value string) *models.ValidationError {
	if strings.TrimSpace(value) == "" {
		return &models.ValidationError{
			Field:   fieldName,
			Message: fmt.Sprintf("%s is required", fieldName),
			Value:   value,
		}
	}
	return nil
}
