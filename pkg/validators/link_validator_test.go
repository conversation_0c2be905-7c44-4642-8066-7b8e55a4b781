package validators

import (
	"testing"

	"github.com/stagedates/shortlink-service/pkg/models"
)

func TestLinkValidator_ValidateCreateLinkRequest(t *testing.T) {
	validator := NewLinkValidator()

	tests := []struct {
		name          string
		request       *models.CreateLinkRequest
		expectedErrors int
		expectedFields []string
	}{
		{
			name: "valid request with both URLs",
			request: &models.CreateLinkRequest{
				TargetURL: "https://example.com/path",
				SrcURL:    "https://short.ly/abc123",
			},
			expectedErrors: 0,
			expectedFields: []string{},
		},
		{
			name: "valid request with only target URL",
			request: &models.CreateLinkRequest{
				TargetURL: "https://example.com/path",
			},
			expectedErrors: 0,
			expectedFields: []string{},
		},
		{
			name: "missing target URL",
			request: &models.CreateLinkRequest{
				SrcURL: "https://short.ly/abc123",
			},
			expectedErrors: 1,
			expectedFields: []string{"targetUrl"},
		},
		{
			name: "empty target URL",
			request: &models.CreateLinkRequest{
				TargetURL: "",
				SrcURL:    "https://short.ly/abc123",
			},
			expectedErrors: 1,
			expectedFields: []string{"targetUrl"},
		},
		{
			name: "invalid target URL",
			request: &models.CreateLinkRequest{
				TargetURL: "not-a-valid-url",
				SrcURL:    "https://short.ly/abc123",
			},
			expectedErrors: 1,
			expectedFields: []string{"targetUrl"},
		},
		{
			name: "invalid source URL",
			request: &models.CreateLinkRequest{
				TargetURL: "https://example.com/path",
				SrcURL:    "not-a-valid-url",
			},
			expectedErrors: 1,
			expectedFields: []string{"shortlink"},
		},
		{
			name: "both URLs invalid",
			request: &models.CreateLinkRequest{
				TargetURL: "not-a-valid-url",
				SrcURL:    "also-not-valid",
			},
			expectedErrors: 2,
			expectedFields: []string{"targetUrl", "shortlink"},
		},
		{
			name: "URL without scheme",
			request: &models.CreateLinkRequest{
				TargetURL: "example.com/path",
			},
			expectedErrors: 1,
			expectedFields: []string{"targetUrl"},
		},
		{
			name: "URL with invalid scheme",
			request: &models.CreateLinkRequest{
				TargetURL: "ftp://example.com/path",
			},
			expectedErrors: 1,
			expectedFields: []string{"targetUrl"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			errors := validator.ValidateCreateLinkRequest(tt.request)

			if len(errors) != tt.expectedErrors {
				t.Errorf("expected %d errors, got %d", tt.expectedErrors, len(errors))
			}

			// Check that expected fields are present in errors
			errorFields := make(map[string]bool)
			for _, err := range errors {
				errorFields[err.Field] = true
			}

			for _, expectedField := range tt.expectedFields {
				if !errorFields[expectedField] {
					t.Errorf("expected error for field %s, but not found", expectedField)
				}
			}
		})
	}
}

func TestLinkValidator_isValidURL(t *testing.T) {
	validator := NewLinkValidator()

	tests := []struct {
		name     string
		url      string
		expected bool
	}{
		{"valid HTTPS URL", "https://example.com", true},
		{"valid HTTP URL", "http://example.com", true},
		{"valid URL with path", "https://example.com/path/to/resource", true},
		{"valid URL with query", "https://example.com?param=value", true},
		{"empty string", "", false},
		{"no scheme", "example.com", false},
		{"invalid scheme", "ftp://example.com", false},
		{"no host", "https://", false},
		{"malformed URL", "https:///path", false},
		{"just scheme", "https", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := validator.isValidURL(tt.url)
			if result != tt.expected {
				t.Errorf("isValidURL(%q) = %v, expected %v", tt.url, result, tt.expected)
			}
		})
	}
}

func TestLinkValidator_ValidateRequired(t *testing.T) {
	validator := NewLinkValidator()

	tests := []struct {
		name      string
		fieldName string
		value     string
		expectErr bool
	}{
		{"valid value", "testField", "some value", false},
		{"empty string", "testField", "", true},
		{"whitespace only", "testField", "   ", true},
		{"tab and spaces", "testField", "\t  \n", true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validator.ValidateRequired(tt.fieldName, tt.value)
			if tt.expectErr && err == nil {
				t.Errorf("expected error for ValidateRequired(%q, %q), got nil", tt.fieldName, tt.value)
			}
			if !tt.expectErr && err != nil {
				t.Errorf("expected no error for ValidateRequired(%q, %q), got %v", tt.fieldName, tt.value, err)
			}
		})
	}
}
