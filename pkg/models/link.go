package models

import (
	"time"

	"github.com/google/uuid"
)

type Link struct {
	ID         uuid.UUID `json:"id" db:"id"`
	SrcURL     string    `json:"shortlink" db:"src_url"`
	TargetURL  string    `json:"targetUrl" db:"target_url"`
	InsertedAt time.Time `json:"insertedAt" db:"inserted_at"`
	UpdatedAt  time.Time `json:"updatedAt" db:"updated_at"`
}

type CreateLinkRequest struct {
	TargetURL string `json:"targetUrl" validate:"required,url" example:"https://example.com/some/path"`
	SrcURL    string `json:"shortlink,omitempty" validate:"omitempty,url" example:"https://stgdts.com/abc123"`
}

type CreateLinkResponse struct {
	ID         uuid.UUID `json:"id" example:"123e4567-e89b-12d3-a456-************"`
	SrcURL     string    `json:"shortlink" example:"https://stgdts.com/abc123"`
	TargetURL  string    `json:"targetUrl" example:"https://example.com/some/path"`
	InsertedAt time.Time `json:"insertedAt" example:"2023-01-01T12:00:00Z"`
	UpdatedAt  time.Time `json:"updatedAt" example:"2023-01-01T12:00:00Z"`
}

type ValidationError struct {
	Field   string `json:"field" example:"targetUrl"`
	Message string `json:"message" example:"targetUrl is required"`
	Value   string `json:"value,omitempty" example:"invalid-url"`
}

type ErrorResponse struct {
	ErrorCode string            `json:"errorCode" example:"VALIDATION_ERROR"`
	Message   string            `json:"message" example:"Request validation failed"`
	Data      []ValidationError `json:"data,omitempty"`
}

const (
	ErrorCodeValidation    = "VALIDATION_ERROR"
	ErrorCodeUnauthorized  = "UNAUTHORIZED"
	ErrorCodeInternalError = "INTERNAL_ERROR"
	ErrorCodeDuplicateLink = "DUPLICATE_LINK"
	ErrorCodeInvalidURL    = "INVALID_URL"
	ErrorCodeNotFound      = "NOT_FOUND"
)

func (l *Link) ToCreateLinkResponse() *CreateLinkResponse {
	return &CreateLinkResponse{
		ID:         l.ID,
		SrcURL:     l.SrcURL,
		TargetURL:  l.TargetURL,
		InsertedAt: l.InsertedAt,
		UpdatedAt:  l.UpdatedAt,
	}
}
