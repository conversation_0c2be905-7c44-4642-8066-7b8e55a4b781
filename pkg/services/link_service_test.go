package services

import (
	"context"
	"testing"

	"github.com/google/uuid"
	"github.com/stagedates/shortlink-service/pkg/errors"
	"github.com/stagedates/shortlink-service/pkg/models"
	"github.com/stagedates/shortlink-service/pkg/validators"
)

// MockLinkRepository implements repository.LinkRepository for testing
type MockLinkRepository struct {
	links       map[string]*models.Link
	srcURLIndex map[string]*models.Link
	shouldError bool
	errorType   string
}

func NewMockLinkRepository() *MockLinkRepository {
	return &MockLinkRepository{
		links:       make(map[string]*models.Link),
		srcURLIndex: make(map[string]*models.Link),
		shouldError: false,
	}
}

func (m *MockLinkRepository) Create(ctx context.Context, link *models.Link) (*models.Link, error) {
	if m.shouldError && m.errorType == "create" {
		return nil, errors.NewInternalError("mock create error", nil)
	}

	link.ID = uuid.New()
	m.links[link.ID.String()] = link
	m.srcURLIndex[link.SrcURL] = link
	return link, nil
}

func (m *MockLinkRepository) GetByID(ctx context.Context, id uuid.UUID) (*models.Link, error) {
	if m.shouldError && m.errorType == "getbyid" {
		return nil, errors.NewInternalError("mock get error", nil)
	}

	if link, exists := m.links[id.String()]; exists {
		return link, nil
	}
	return nil, errors.NewInternalError("link not found", nil)
}

func (m *MockLinkRepository) GetBySrcURL(ctx context.Context, srcURL string) (*models.Link, error) {
	if m.shouldError && m.errorType == "getbysrcurl" {
		return nil, errors.NewInternalError("mock get error", nil)
	}

	if link, exists := m.srcURLIndex[srcURL]; exists {
		return link, nil
	}
	return nil, errors.NewInternalError("link not found", nil)
}

func (m *MockLinkRepository) GetByTargetURL(ctx context.Context, targetURL string) (*models.Link, error) {
	return nil, errors.NewInternalError("not implemented", nil)
}

func (m *MockLinkRepository) Update(ctx context.Context, link *models.Link) (*models.Link, error) {
	return nil, errors.NewInternalError("not implemented", nil)
}

func (m *MockLinkRepository) Delete(ctx context.Context, id uuid.UUID) error {
	return errors.NewInternalError("not implemented", nil)
}

func (m *MockLinkRepository) ExistsBySrcURL(ctx context.Context, srcURL string) (bool, error) {
	if m.shouldError && m.errorType == "exists" {
		return false, errors.NewInternalError("mock exists error", nil)
	}

	_, exists := m.srcURLIndex[srcURL]
	return exists, nil
}

func (m *MockLinkRepository) SetError(errorType string) {
	m.shouldError = true
	m.errorType = errorType
}

func (m *MockLinkRepository) ClearError() {
	m.shouldError = false
	m.errorType = ""
}

// MockGofrApp implements basic gofr.App functionality for testing
type MockGofrApp struct {
	config map[string]string
}

func NewMockGofrApp() *MockGofrApp {
	return &MockGofrApp{
		config: make(map[string]string),
	}
}

func (m *MockGofrApp) GetOrDefault(key, defaultValue string) string {
	if value, exists := m.config[key]; exists {
		return value
	}
	return defaultValue
}

func (m *MockGofrApp) SetConfig(key, value string) {
	m.config[key] = value
}

func TestLinkService_CreateLink(t *testing.T) {
	tests := []struct {
		name            string
		request         *models.CreateLinkRequest
		setupMock       func(*MockLinkRepository, *MockGofrApp)
		expectError     bool
		expectedErrCode string
	}{
		{
			name: "successful creation with target URL only",
			request: &models.CreateLinkRequest{
				TargetURL: "https://example.com/path",
			},
			setupMock: func(repo *MockLinkRepository, app *MockGofrApp) {
				// Mock setup not needed for this test
			},
			expectError: false,
		},
		{
			name: "successful creation with both URLs",
			request: &models.CreateLinkRequest{
				TargetURL: "https://example.com/path",
				SrcURL:    "https://short.ly/custom",
			},
			setupMock: func(repo *MockLinkRepository, app *MockGofrApp) {
				// Mock setup not needed for this test
			},
			expectError: false,
		},
		{
			name: "validation error - missing target URL",
			request: &models.CreateLinkRequest{
				SrcURL: "https://short.ly/custom",
			},
			setupMock:       func(repo *MockLinkRepository, app *MockGofrApp) {},
			expectError:     true,
			expectedErrCode: models.ErrorCodeValidation,
		},
		{
			name: "validation error - invalid target URL",
			request: &models.CreateLinkRequest{
				TargetURL: "not-a-valid-url",
			},
			setupMock:       func(repo *MockLinkRepository, app *MockGofrApp) {},
			expectError:     true,
			expectedErrCode: models.ErrorCodeValidation,
		},
		{
			name: "duplicate source URL error",
			request: &models.CreateLinkRequest{
				TargetURL: "https://example.com/path",
				SrcURL:    "https://stagedates.com/existing",
			},
			setupMock: func(repo *MockLinkRepository, app *MockGofrApp) {
				// Pre-populate with existing link - use the normalized URL
				existingLink := &models.Link{
					ID:        uuid.New(),
					SrcURL:    "https://stagedates.com/existing",
					TargetURL: "https://other.com",
				}
				repo.srcURLIndex["https://stagedates.com/existing"] = existingLink
			},
			expectError:     true,
			expectedErrCode: models.ErrorCodeDuplicateLink,
		},
		{
			name: "repository error during creation",
			request: &models.CreateLinkRequest{
				TargetURL: "https://example.com/path",
			},
			setupMock: func(repo *MockLinkRepository, app *MockGofrApp) {
				repo.SetError("create")
			},
			expectError:     true,
			expectedErrCode: models.ErrorCodeInternalError,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup
			repo := NewMockLinkRepository()
			app := NewMockGofrApp()
			validator := validators.NewLinkValidator()

			// Create a nil gofr.App for testing (we'll handle config in the service)
			service := NewLinkService(repo, validator, nil)

			tt.setupMock(repo, app)

			// Execute
			ctx := context.Background()
			result, err := service.CreateLink(ctx, tt.request)

			// Verify
			if tt.expectError {
				if err == nil {
					t.Errorf("expected error, got nil")
					return
				}

				if appErr, ok := errors.IsAppError(err); ok {
					if appErr.Code != tt.expectedErrCode {
						t.Errorf("expected error code %s, got %s", tt.expectedErrCode, appErr.Code)
					}
				} else {
					t.Errorf("expected AppError, got %T", err)
				}
			} else {
				if err != nil {
					t.Errorf("expected no error, got %v", err)
					return
				}

				if result == nil {
					t.Errorf("expected result, got nil")
					return
				}

				// Verify the response structure
				if result.TargetURL != tt.request.TargetURL {
					t.Errorf("expected target URL %s, got %s", tt.request.TargetURL, result.TargetURL)
				}

				if result.ID == uuid.Nil {
					t.Errorf("expected valid UUID, got nil UUID")
				}

				if result.SrcURL == "" {
					t.Errorf("expected source URL to be generated, got empty string")
				}
			}
		})
	}
}
