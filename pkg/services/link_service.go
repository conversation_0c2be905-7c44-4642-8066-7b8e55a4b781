package services

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"net/url"
	"strings"

	"github.com/google/uuid"
	"github.com/stagedates/shortlink-service/pkg/errors"
	"github.com/stagedates/shortlink-service/pkg/models"
	"github.com/stagedates/shortlink-service/pkg/repository"
	"github.com/stagedates/shortlink-service/pkg/validators"
	"gofr.dev/pkg/gofr"
)

type LinkServiceConfig struct {
	BaseURL string
}

type LinkService interface {
	CreateLink(ctx context.Context, req *models.CreateLinkRequest) (*models.CreateLinkResponse, error)
	GetLinkByID(ctx context.Context, id uuid.UUID) (*models.Link, error)
	GetLinkBySrcURL(ctx context.Context, srcURL string) (*models.Link, error)
}

type linkService struct {
	repo      repository.LinkRepository
	validator *validators.LinkValidator
	config    *LinkServiceConfig
}

func NewLinkService(repo repository.LinkRepository, validator *validators.LinkValidator, app *gofr.App) LinkService {
	config := &LinkServiceConfig{
		BaseURL: initializeBaseURL(app),
	}

	return &linkService{
		repo:      repo,
		validator: validator,
		config:    config,
	}
}

func initializeBaseURL(app *gofr.App) string {
	const defaultBaseURL = "https://stagedates.com"

	var baseURL string
	if app != nil {
		baseURL = app.Config.GetOrDefault("SHORTLINK_BASE_URL", defaultBaseURL)
	} else {
		baseURL = defaultBaseURL
	}

	return strings.TrimRight(baseURL, "/")
}

func (s *linkService) CreateLink(ctx context.Context, req *models.CreateLinkRequest) (*models.CreateLinkResponse, error) {
	validationErrors := s.validator.ValidateCreateLinkRequest(req)
	if len(validationErrors) > 0 {
		return nil, errors.NewValidationError("Request validation failed", validationErrors)
	}

	srcURL := req.SrcURL
	if srcURL == "" {
		var err error
		srcURL, err = s.generateShortURL()
		if err != nil {
			return nil, errors.NewInternalError("Failed to generate short URL", err)
		}
	} else {
		srcURL = s.normalizeSourceURL(srcURL)
	}

	exists, err := s.repo.ExistsBySrcURL(ctx, srcURL)
	if err != nil {
		return nil, err
	}
	if exists {
		return nil, errors.NewDuplicateLinkError("A link with this short URL already exists")
	}

	link := &models.Link{
		SrcURL:    srcURL,
		TargetURL: req.TargetURL,
	}

	createdLink, err := s.repo.Create(ctx, link)
	if err != nil {
		return nil, err
	}

	return createdLink.ToCreateLinkResponse(), nil
}

func (s *linkService) GetLinkByID(ctx context.Context, id uuid.UUID) (*models.Link, error) {
	return s.repo.GetByID(ctx, id)
}

func (s *linkService) GetLinkBySrcURL(ctx context.Context, srcURL string) (*models.Link, error) {
	return s.repo.GetBySrcURL(ctx, srcURL)
}

func (s *linkService) generateShortURL() (string, error) {
	path := s.generateRandomPath()
	baseURL := strings.TrimRight(s.config.BaseURL, "/") + "/"

	return baseURL + path, nil
}

func (s *linkService) generateRandomPath() string {
	bytes := make([]byte, 3)
	rand.Read(bytes)
	return hex.EncodeToString(bytes)
}

func (s *linkService) normalizeSourceURL(srcURL string) string {
	parsed, err := url.Parse(srcURL)
	if err != nil {
		return srcURL
	}

	var path string
	if parsed.Path != "" && parsed.Path != "/" {
		path = strings.TrimPrefix(parsed.Path, "/")
	}

	if path == "" {
		path = s.generateRandomPath()
	}

	baseURL := strings.TrimRight(s.config.BaseURL, "/")

	return baseURL + "/" + path
}
