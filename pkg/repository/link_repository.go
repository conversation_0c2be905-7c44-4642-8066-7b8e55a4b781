package repository

import (
	"context"
	"database/sql"
	"time"

	"github.com/google/uuid"
	"github.com/stagedates/shortlink-service/pkg/errors"
	"github.com/stagedates/shortlink-service/pkg/models"
	"gofr.dev/pkg/gofr"
)

type LinkRepository interface {
	Create(ctx context.Context, link *models.Link) (*models.Link, error)
	GetByID(ctx context.Context, id uuid.UUID) (*models.Link, error)
	GetBySrcURL(ctx context.Context, srcURL string) (*models.Link, error)
	GetByTargetURL(ctx context.Context, targetURL string) (*models.Link, error)
	Update(ctx context.Context, link *models.Link) (*models.Link, error)
	Delete(ctx context.Context, id uuid.UUID) error
	ExistsBySrcURL(ctx context.Context, srcURL string) (bool, error)
}

type PostgresLinkRepository struct {
	db *gofr.Context
}

func NewPostgresLinkRepository(db *gofr.Context) LinkRepository {
	return &PostgresLinkRepository{
		db: db,
	}
}

func (r *PostgresLinkRepository) Create(ctx context.Context, link *models.Link) (*models.Link, error) {
	now := time.Now()
	link.InsertedAt = now
	link.UpdatedAt = now

	query := `
		INSERT INTO shortlink.link (src_url, target_url, inserted_at, updated_at) 
		VALUES ($1, $2, $3, $4) 
		RETURNING id`

	var linkID uuid.UUID
	err := r.db.SQL.QueryRowContext(ctx, query,
		link.SrcURL, link.TargetURL, link.InsertedAt, link.UpdatedAt).Scan(&linkID)

	if err != nil {
		r.db.Logger.Errorf("failed to create link: %v", err)
		return nil, errors.NewInternalError("failed to create link", err)
	}

	link.ID = linkID
	return link, nil
}

func (r *PostgresLinkRepository) GetByID(ctx context.Context, id uuid.UUID) (*models.Link, error) {
	query := `
		SELECT id, src_url, target_url, inserted_at, updated_at 
		FROM shortlink.link 
		WHERE id = $1`

	var link models.Link
	err := r.db.SQL.QueryRowContext(ctx, query, id).Scan(
		&link.ID, &link.SrcURL, &link.TargetURL, &link.InsertedAt, &link.UpdatedAt)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.NewNotFoundError("link not found")
		}
		r.db.Logger.Errorf("failed to get link by ID: %v", err)
		return nil, errors.NewInternalError("failed to retrieve link", err)
	}

	return &link, nil
}

func (r *PostgresLinkRepository) GetBySrcURL(ctx context.Context, srcURL string) (*models.Link, error) {
	query := `
		SELECT id, src_url, target_url, inserted_at, updated_at 
		FROM shortlink.link 
		WHERE src_url = $1`

	var link models.Link
	err := r.db.SQL.QueryRowContext(ctx, query, srcURL).Scan(
		&link.ID, &link.SrcURL, &link.TargetURL, &link.InsertedAt, &link.UpdatedAt)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.NewInternalError("link not found", err)
		}
		r.db.Logger.Errorf("failed to get link by src URL: %v", err)
		return nil, errors.NewInternalError("failed to retrieve link", err)
	}

	return &link, nil
}

func (r *PostgresLinkRepository) GetByTargetURL(ctx context.Context, targetURL string) (*models.Link, error) {
	query := `
		SELECT id, src_url, target_url, inserted_at, updated_at 
		FROM shortlink.link 
		WHERE target_url = $1`

	var link models.Link
	err := r.db.SQL.QueryRowContext(ctx, query, targetURL).Scan(
		&link.ID, &link.SrcURL, &link.TargetURL, &link.InsertedAt, &link.UpdatedAt)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.NewInternalError("link not found", err)
		}
		r.db.Logger.Errorf("failed to get link by target URL: %v", err)
		return nil, errors.NewInternalError("failed to retrieve link", err)
	}

	return &link, nil
}

func (r *PostgresLinkRepository) Update(ctx context.Context, link *models.Link) (*models.Link, error) {
	// Create a copy to avoid mutating the input parameter
	updatedLink := *link
	updatedLink.UpdatedAt = time.Now()

	query := `
		UPDATE shortlink.link
		SET src_url = $2, target_url = $3, updated_at = $4
		WHERE id = $1
		RETURNING id, src_url, target_url, inserted_at, updated_at`

	err := r.db.SQL.QueryRowContext(ctx, query,
		updatedLink.ID, updatedLink.SrcURL, updatedLink.TargetURL, updatedLink.UpdatedAt).Scan(
		&updatedLink.ID, &updatedLink.SrcURL, &updatedLink.TargetURL, &updatedLink.InsertedAt, &updatedLink.UpdatedAt)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.NewNotFoundError("link not found")
		}
		r.db.Logger.Errorf("failed to update link: %v", err)
		return nil, errors.NewInternalError("failed to update link", err)
	}

	return &updatedLink, nil
}

func (r *PostgresLinkRepository) Delete(ctx context.Context, id uuid.UUID) error {
	query := `DELETE FROM shortlink.link WHERE id = $1`

	result, err := r.db.SQL.ExecContext(ctx, query, id)
	if err != nil {
		r.db.Logger.Errorf("failed to delete link: %v", err)
		return errors.NewInternalError("failed to delete link", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		r.db.Logger.Errorf("failed to get rows affected: %v", err)
		return errors.NewInternalError("failed to verify deletion", err)
	}

	if rowsAffected == 0 {
		return errors.NewInternalError("link not found", nil)
	}

	return nil
}

func (r *PostgresLinkRepository) ExistsBySrcURL(ctx context.Context, srcURL string) (bool, error) {
	query := `SELECT EXISTS(SELECT 1 FROM shortlink.link WHERE src_url = $1)`

	var exists bool
	err := r.db.SQL.QueryRowContext(ctx, query, srcURL).Scan(&exists)
	if err != nil {
		r.db.Logger.Errorf("failed to check link existence: %v", err)
		return false, errors.NewInternalError("failed to check link existence", err)
	}

	return exists, nil
}
