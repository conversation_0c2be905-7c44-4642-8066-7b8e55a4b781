package handlers

import (
	"github.com/stagedates/shortlink-service/pkg/errors"
	"github.com/stagedates/shortlink-service/pkg/models"
	"github.com/stagedates/shortlink-service/pkg/repository"
	"github.com/stagedates/shortlink-service/pkg/services"
	"github.com/stagedates/shortlink-service/pkg/validators"
	"gofr.dev/pkg/gofr"
)

type LinkHandler struct {
	linkService services.LinkService
	validator   *validators.LinkValidator
	app         *gofr.App
}

func NewLinkHandler(linkService services.LinkService, validator *validators.LinkValidator, app *gofr.App) *LinkHandler {
	return &LinkHandler{
		linkService: linkService,
		validator:   validator,
		app:         app,
	}
}

func (h *LinkHandler) CreateLink(ctx *gofr.Context) (any, error) {
	var req models.CreateLinkRequest
	if err := ctx.Bind(&req); err != nil {
		return nil, errors.NewValidationError("Invalid request format", []models.ValidationError{
			{
				Field:   "body",
				Message: "Request body must be valid JSON",
				Value:   "",
			},
		})
	}

	apiKey, _ := ctx.Value("RequestApiKey").(string)
	if !validators.ApiKeyValidator(h.app, apiKey) {
		ctx.Logger.Warn("Unauthorized: Invalid or missing API key")
		return nil, errors.NewUnauthorizedError("Invalid or missing API key")
	}

	response, err := h.linkService.CreateLink(ctx, &req)
	if err != nil {
		return nil, err
	}

	return response, nil
}

func FirebaseLinkCreationHandler(app *gofr.App) func(*gofr.Context) (any, error) {
	validator := validators.NewLinkValidator()

	return func(ctx *gofr.Context) (any, error) {
		repo := repository.NewPostgresLinkRepository(ctx)
		linkService := services.NewLinkService(repo, validator, app)
		handler := NewLinkHandler(linkService, validator, app)

		return handler.CreateLink(ctx)
	}
}
