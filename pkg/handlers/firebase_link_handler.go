package handlers

import (
	"errors"
	"strings"

	"gofr.dev/pkg/gofr"
	"gofr.dev/pkg/gofr/http/response"
)

const fallbackUrl = "https://stagedates.com"

var (
	ErrPublicGenericError                      = errors.New("an error has occured while processing your request. please try again later")
	ErrDbResultsNotFound                       = errors.New("FirebaseLinkHandler(): no results found - failed to retrieve target URL")
	ErrShortLinkIdentifierUrlParameterNotFound = errors.New("FirebaseLinkHandler(): short link identifier url parameter required but not found")
	fallbackRedirect                           = response.Redirect{
		URL: fallbackUrl,
	}
)

type FirebaseLink struct {
	ID                         string `json:"id"`
	SrcURL                     string `json:"src_url"`
	TargetURL                  string `json:"target_url"`
	TrackingImpressionsCounter int    `json:"tracking_impressions_counter"`
}

// FirebaseLinkHandler handles requests to the root (/) route for Firebase links
func FirebaseLinkHandler(ctx *gofr.Context) (interface{}, error) {
	// Get the identifier from the URL path
	_identifier := ctx.PathParam("identifier")
	identifier := strings.TrimPrefix(_identifier, "/")

	ctx.Debugf("FirebaseLinkHandler(): identifier after: %v", identifier)

	if identifier == "" {
		ctx.Logf(ErrShortLinkIdentifierUrlParameterNotFound.Error())
		return nil, ErrPublicGenericError
	}

	if identifier == "health" {
		return "ok", nil
	}

	ctx.Logf("FirebaseLinkHandler(): identifier: %v", identifier)

	query := "SELECT id, target_url, tracking_impressions_counter FROM shortlink.link WHERE src_url ILIKE $1"
	// unfortunately, no transactions supported in GoFr. Therefore, a classical query is used.
	rows, err := ctx.SQL.QueryContext(ctx, query, "%"+identifier+"%")

	if err != nil {
		ctx.Logf("FirebaseLinkHandler(): %v", ErrDbResultsNotFound)
		return nil, ErrPublicGenericError
	}

	defer rows.Close()
	var link FirebaseLink
	found := false

	if rows.Next() {
		if err := rows.Scan(&link.ID, &link.TargetURL, &link.TrackingImpressionsCounter); err != nil {
			ctx.Logf("FirebaseLinkHandler(): %v", err)
			return fallbackRedirect, nil
		}
		found = true
	}

	if found {
		if err := validateURL(link.TargetURL); err != nil {
			ctx.Logf(ErrInvalidUrl.Error())
			return nil, ErrPublicGenericError
		}

		// unfortunately, no transactions supported in GoFr. Therefore, a classical query is used
		// Increment the tracking counter for the successful redirect
		updateQuery := "UPDATE link SET tracking_impressions_counter = tracking_impressions_counter + 1 WHERE id = $1"
		_, err := ctx.SQL.ExecContext(ctx, updateQuery, link.ID)
		if err != nil {
			ctx.Logf("FirebaseLinkHandler(): failed to update tracking counter: %v", err)
			// Don't return error - we still want to redirect even if tracking update fails, for better user experience
		}

		ctx.Debugf("FirebaseLinkHandler(): Redirecting to URL", "target_url", link.TargetURL)
		return response.Redirect{URL: link.TargetURL}, nil
	} else {
		ctx.Logf(ErrDbResultsNotFound.Error())
		return nil, ErrPublicGenericError
	}
}
