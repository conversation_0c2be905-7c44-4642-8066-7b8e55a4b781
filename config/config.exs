# This file is responsible for configuring your application
# and its dependencies with the aid of the Config module.
#
# This configuration file is loaded before any dependency and
# is restricted to this project.

# General application configuration
import Config

config :db_seeds,
  # Configure esbuild (the version is required)
  ensure_all_started: ~w(timex)a

config :esbuild,
  version: "0.17.11",
  default: [
    args:
      ~w(js/app.js --bundle --target=es2017 --outdir=../priv/static/assets --external:/fonts/* --external:/images/*),
    cd: Path.expand("../assets", __DIR__),
    env: %{"NODE_PATH" => Path.expand("../deps", __DIR__)}
  ]

config :ex_cldr,
  json_library: Jason

config :ex_firebase_auth_plug, environment: :dev

config :ex_money,
  exchange_rates_retrieve_every: 300_000,
  api_module: Money.ExchangeRates.OpenExchangeRates,
  callback_module: Money.ExchangeRates.Callback,
  exchange_rates_cache_module: Money.ExchangeRates.Cache.Ets,
  preload_historic_rates: nil,
  retriever_options: nil,
  log_failure: :warning,
  log_info: :info,
  log_success: :debug,
  json_library: Jason,
  default_cldr_backend: OrdersService.Cldr,
  exclude_protocol_implementations: []

config :joken, default_signer: System.get_env("BACKEND_API_TOKEN")

config :logger, :console,
  level: :debug,
  format: "$time $metadata[$level] $message\n",
  metadata: [:request_id]

# OpenTelemetry configuration
config :o11y, :attribute_namespace, "app"

config :orders_service, Oban,
  prefix: "orders",
  repo: OrdersService.Repo,
  plugins: [
    Oban.Plugins.Pruner,
    {Oban.Plugins.Cron,
     crontab: [
       {"* * * * *", OrdersService.Workers.OrderMonitorWorker}
     ]}
  ],
  queues: [
    emails: 10,
    monitoring: 1,
    order_publishers: 10,
    seats_release: 10,
    ticket_counters: 10
  ]

config :orders_service, OrdersService.Repo,
  username: System.get_env("POSTGRES_USER"),
  password: System.get_env("POSTGRES_PASSWORD"),
  hostname: System.get_env("POSTGRES_HOST", "localhost"),
  database: System.get_env("POSTGRES_DB", "backend"),
  port: System.get_env("POSTGRES_PORT", "5432"),
  stacktrace: true,
  show_sensitive_data_on_connection_error: true,
  pool_size: 10,
  migration_source: "orders_schema_migrations",
  seed_source: "orders_seeds"

config :orders_service, OrdersServiceWeb.Endpoint,
  url: [host: "localhost"],
  render_errors: [
    formats: [json: OrdersServiceWeb.ErrorJSON],
    layout: false
  ],
  pubsub_server: OrdersService.PubSub,
  live_view: [signing_salt: "j5va1rRH"]

config :orders_service, OrdersServiceWeb.Gettext,
  priv: "priv/gettext/live",
  default_locale: "de",
  default_domain: "app",
  locales: ~w(de en)

# Configures the endpoint
config :orders_service, :basic_auth,
  username: "adyen",
  password: "super-secret"

config :orders_service,
  ecto_repos: [OrdersService.Repo],
  generators: [binary_id: true]

config :orders_service,
  frontend_url: System.get_env("FRONTEND_URL", "https://stagedates.com"),
  redirect_url: "#{System.get_env("FRONTEND_URL", "https://stagedates.com")}/payment/redirect",
  mail_pubsub_topic: System.get_env("MAIL_PUBSUB_TOPIC", "email.email"),
  pdf_pubsub_topic: System.get_env("PDF_PUBSUB_TOPIC", "generate-pdf-v1"),
  ticket_topic: System.get_env("TICKET_TOPIC", "orders.tickets"),
  event_update_conversation_subscription_name:
    System.get_env(
      "EVENT_UPDATE_CONVERSATION_SUBSCRIPTION_NAME",
      "sub-orders-worker.coms.event_update_conversation"
    ),
  gcloud_project: System.get_env("GCLOUD_PROJECT")

# Import environment specific config. This must remain at the bottom

# Use Jason for JSON parsing in Phoenix
# of this file so it overrides the configuration defined above.
config :phoenix, :json_library, Jason

config :tesla, disable_deprecated_builder_warning: true

import_config "#{config_env()}.exs"
