import Config

alias OrdersService.Util.Secret

# config/runtime.exs is executed for all environments, including
# during releases. It is executed after compilation and before the
# system starts, so it is typically used to load production configuration
# and secrets from environment variables or elsewhere. Do not define
# any compile-time configuration in here, as it won't be applied.
# The block below contains prod specific runtime configuration.

# ## Using releases
#
# If you use `mix release`, you need to explicitly enable the server
# by passing the PHX_SERVER=true when you start it:
#
#     PHX_SERVER=true bin/orders_service start
#
# Alternatively, you can use `mix phx.gen.release` to generate a `bin/server`
# script that automatically sets the env var above.
if Secrets.get_secret("PHX_SERVER") do
  config :orders_service, OrdersServiceWeb.Endpoint, server: true
end

config :ex_ikarus,
  environment: String.to_atom(System.get_env("ENVIRONMENT", "prod"))

config :ex_rbac, :casdoor,
  casdoor_certificate: Secrets.get_secret!("CASDOOR_CERTIFICATE"),
  application_id: Secrets.get_secret!("CASDOOR_APPLICATION_ID"),
  application_secret: Secrets.get_secret!("CASDOOR_APPLICATION_SECRET"),
  casdoor_url: System.get_env("CASDOOR_URL", "https://auth.prod.stagedates.it"),
  username: Secrets.get_secret!("CASDOOR_USERNAME"),
  password: Secrets.get_secret!("CASDOOR_PASSWORD")

config :google_api_pub_sub,
  # the base_url is used for publishing messages via pubsub emulator, it's only needed in Minikube, so we don't need
  # If desired, both `http:` and `https:` keys can be

  # the ENV in the .env.template or in the dev / prod systems
  # configured to run both http and https servers on
  # different ports.

  base_url: System.get_env("PUBSUB_BASE_URL", "https://pubsub.googleapis.com")

config :opentelemetry, :processors,
  otel_batch_processor: %{
    exporter:
      {:opentelemetry_exporter,
       %{
         endpoints: [
           System.get_env(
             "OTEL_EXPORTER_OTLP_ENDPOINT",
             "http://opentelemetry-collector.opentelemetry.svc.cluster.local:4318"
           )
         ]
       }}
  }

config :opentelemetry, :resource, service: %{name: "ORDERS_SERVICE"}

config :opentelemetry,
  sampler:
    {:parent_based,
     %{
       root: {:trace_id_ratio_based, 0.10},
       remote_parent_sampled: :always_on,
       remote_parent_not_sampled: :always_off,
       local_parent_sampled: :always_on,
       local_parent_not_sampled: :always_off
     }}

config :opentelemetry,
  text_map_propagators: [:baggage, :trace_context],
  span_processor: :batch,
  traces_exporter: :otlp

config :orders_service, :basic_auth,
  username: "adyen",
  password: Secrets.get_secret("ADYEN_WEBHOOK_PASSWORD")

config :orders_service, :casdoor,
  organization_name: System.get_env("CASDOOR_ORGANIZATION_NAME", "stagedates"),
  organizer_domain_name: System.get_env("CASDOOR_ORGANIZER_DOMAIN_NAME", "promoter"),
  user_model_name: System.get_env("CASDOOR_USER_MODEL_NAME", "user_model"),
  user_enforcer_name: System.get_env("CASDOOR_USER_ENFORCER_NAME", "user_enforcer")

config :orders_service,
  environment: String.to_atom(System.get_env("ENVIRONMENT", "prod")),
  log_level: System.get_env("LOG_LEVEL", "info"),
  frontend_url: System.get_env("FRONTEND_URL", "https://stagedates.com"),
  redirect_url: "#{System.get_env("FRONTEND_URL", "http://stagedates.com")}/payment/redirect",
  mail_pubsub_topic: System.get_env("MAIL_PUBSUB_TOPIC", "email.email"),
  pdf_pubsub_topic: System.get_env("PDF_PUBSUB_TOPIC", "generate-pdf-v1"),
  events_pubsub_subscription: System.get_env("EVENTS_PUBSUB_SUBSCRIPTION", "orders-worker.events.events"),
  future_demand_events_pubsub_subscription:
    System.get_env(
      "FUTURE_DEMAND_EVENTS_PUBSUB_SUBSCRIPTION",
      "orders-worker.future-demand.events"
    ),
  events_ticket_categories_subscription_name:
    System.get_env(
      "EVENTS_TICKET_CATEGORIES_PUBSUB_SUBSCRIPTION",
      "orders-worker.events.ticket_categories"
    ),
  events_sales_channels_subscription_name:
    System.get_env(
      "EVENTS_SALES_CHANNELS_PUBSUB_SUBSCRIPTION",
      "orders-worker.events.sales_channels"
    ),
  ticket_secret: Secrets.get_secret("TICKET_SECRET"),
  ticket_topic: System.get_env("TICKET_TOPIC", "orders.tickets"),
  orders_topic: System.get_env("ORDERS_TOPIC", "orders.orders"),
  events_imported_tickets_subscription_name:
    System.get_env(
      "IMPORTED_TICKETS_PUBSUB_SUBSCRIPTION",
      "orders-worker.events.imported_tickets"
    ),
  imported_tickets_topic_name: System.get_env("IMPORTED_TICKETS_PUBSUB_TOPIC", "orders.imported_tickets"),
  event_update_conversation_subscription_name:
    System.get_env(
      "EVENT_UPDATE_CONVERSATION_SUBSCRIPTION_NAME",
      "sub-orders-worker.coms.event_update_conversation"
    ),
  gcloud_project: System.get_env("GCLOUD_PROJECT"),
  db_heartbeat_interval: String.to_integer(System.get_env("DB_HEARTBEAT_INTERVAL", "60")),
  cdn: System.get_env("CDN_URL", "https://storage.googleapis.com"),
  ticket_swap_authorization_token: Secrets.get_secret("TICKET_SWAP_AUTHORIZATION_TOKEN")

# Initialize the Unleash client
config :unleash, Unleash,
  url: "#{System.get_env("UNLEASH_URL", "https://flags.prod.stagedates.it/api")}",
  appname: System.get_env("UNLEASH_APP_NAME", "orders-service"),
  instance_id: System.get_env("UNLEASH_INSTANCE_ID", "orders-service-prod"),
  strategies: Unleash.Strategies,
  custom_http_headers: [{"authorization", Secrets.get_secret("UNLEASH_AUTH_TOKEN")}],
  disable_client: false,
  disable_metrics: config_env() == :dev,
  app_env: config_env()

if config_env() == :prod do
  secret_key_base =
    Secrets.get_secret("SECRET_KEY_BASE") ||
      raise """
      environment variable SECRET_KEY_BASE is missing.
      You can generate one by calling: mix phx.gen.secret
      """

  host = Secrets.get_secret("PHX_HOST") || "stagedates.com"
  path = System.get_env("PHX_PATH") || "/orders"
  port = String.to_integer(Secrets.get_secret("PHX_PORT") || "4000")

  ip =
    if System.get_env("ENVIRONMENT", "local") == "local",
      do: {127, 0, 0, 1},
      else: {0, 0, 0, 0, 0, 0, 0, 0}

  config :adyen,
    environment: String.to_atom(System.get_env("ADYEN_ENVIRONMENT", "live")),
    api_key: [
      checkout_api_key: Secrets.get_secret("ADYEN_PAYMENTS_API_KEY"),
      kyc_api_key: Secrets.get_secret("ADYEN_LEM_API_KEY"),
      # Enable IPv6 and bind on all interfaces.
      # Set it to  {0, 0, 0, 0, 0, 0, 0, 1} for local network only access.
      # See the documentation on https://hexdocs.pm/plug_cowboy/Plug.Cowboy.html
      # for details about using IPv6 vs IPv4 and loopback vs public addresses.
      balance_api_key: Secrets.get_secret("ADYEN_BALANCE_API_KEY"),
      report_api_key: Secrets.get_secret("ADYEN_REPORT_API_KEY")
    ],
    live_prefix: Secrets.get_secret("ADYEN_LIVE_URL_PREFIX"),
    merchant_account: Secrets.get_secret("MERCHANT_ACCOUNT"),
    report_download_url: Secrets.get_secret("ADYEN_REPORT_DOWNLOAD_URL")

  config :ex_firebase_auth, :issuer, Secrets.get_secret("EX_FIREBASE_ISSUER")

  config :ex_firebase_auth_plug,
    environment: String.to_atom(System.get_env("ENVIRONMENT", "prod"))

  config :ex_service_client,
    environment: String.to_atom(System.get_env("ENVIRONMENT", "prod")),
    backend_endpoint: System.get_env("BACKEND_URL", "https://stagedates.com"),
    gke_endpoint: System.get_env("GKE_URL", "https://api.stagedates.com"),
    service_client_id: Secrets.get_secret("CLIENT_ID"),
    service_client_secret: Secrets.get_secret("CLIENT_SECRET"),
    service_name: Secrets.get_secret("SYSTEM")

  config :goth, json: Secrets.get_secret("SERVICE_ACCOUNT")

  config :hammer,
    backend: {Hammer.Backend.ETS, [expiry_ms: 60_000 * 60 * 4, cleanup_interval_ms: 60_000 * 10]}

  config :joken, default_signer: Secrets.get_secret("BACKEND_API_TOKEN")

  config :orders_service, OrdersService.Repo,
    hostname: Secrets.get_secret("POSTGRES_HOST"),
    username: Secrets.get_secret("POSTGRES_USER"),
    password: Secrets.get_secret("POSTGRES_PASSWORD"),
    database: Secrets.get_secret("POSTGRES_DB"),
    port: Secrets.get_secret("POSTGRES_PORT", "5432"),
    after_connect:
      {Postgrex, :query!, ["SET search_path TO #{System.get_env("POSTGRES_SCHEMA", "orders")}, public", []]},
    migration_timestamps: [type: :naive_datetime_usec],
    stacktrace: true,
    show_sensitive_data_on_connection_error: true,
    pool_size: 30,
    migration_source: "orders_schema_migrations",
    seed_source: "orders_seeds"

  config :orders_service, OrdersServiceWeb.Endpoint,
    url: [host: host, path: path],
    http: [
      compress: true,
      ip: ip,
      port: port
    ],
    secret_key_base: secret_key_base

  # database_url =
  # System.get_env("DATABASE_URL") ||
  # raise """
  # environment variable DATABASE_URL is missing.
  # For example: ecto://USER:PASS@HOST/DATABASE
  # """

  # maybe_ipv6 = if System.get_env("ECTO_IPV6") in ~w(true 1), do: [:inet6], else: []

  # config :orders_service, OrdersService.Repo,
  ## ssl: true,
  # url: database_url,
  # pool_size: String.to_integer(System.get_env("POOL_SIZE") || "10"),
  # socket_options: maybe_ipv6

  # Delete after either svc is running in GKE or redis works with cloud run

  # config :hammer,
  #   backend:
  #     {Hammer.Backend.Redis,
  #      [
  #        key_prefix: "orders_svc:draft_rate_limiter",
  #        expiry_ms: 60_000 * 60 * 4,
  #        cleanup_interval_ms: 60_000 * 10,
  #        redix_config: [
  #          host: System.get_env("REDIS_IP"),
  #          port: "REDIS_PORT" |> System.get_env() |> String.to_integer()
  #        ]
  #      ]}

  config :seatsio,
    admin_key: Secrets.get_secret("SEATSIO_ADMIN_KEY"),
    public_default_workspace_key: Secrets.get_secret("SEATSIO_PUBLIC_DEFAULT_WORKSPACE_KEY"),
    private_default_workspace_key: Secrets.get_secret("SEATSIO_PRIVATE_DEFAULT_WORKSPACE_KEY")

  # ## SSL Support
  #
  # To get SSL working, you will need to add the `https` key
  # to your endpoint configuration:
  #
  #     config :orders_service, OrdersServiceWeb.Endpoint,
  #       https: [
  #         ...,
  #         port: 443,
  #         cipher_suite: :strong,
  #         keyfile: System.get_env("SOME_APP_SSL_KEY_PATH"),
  #         certfile: System.get_env("SOME_APP_SSL_CERT_PATH")
  #       ]
  #
  # The `cipher_suite` is set to `:strong` to support only the
  # latest and more secure SSL ciphers. This means old browsers
  # and clients may not be supported. You can set it to
  # `:compatible` for wider support.
  #
  # `:keyfile` and `:certfile` expect an absolute path to the key
  # and cert in disk or a relative path inside priv, for example
  # "priv/ssl/server.key". For all supported SSL configuration
  # options, see https://hexdocs.pm/plug/Plug.SSL.html#configure/1
  #
  # We also recommend setting `force_ssl` in your endpoint, ensuring
  # no data is ever sent via http, always redirecting to https:
  #
  #     config :orders_service, OrdersServiceWeb.Endpoint,
  #       force_ssl: [hsts: true]
  #
  # Check `Plug.SSL` for all available options in `force_ssl`.
end
