import Config

config :adyen,
  environment: :test,
  api_key: [
    checkout_api_key: System.get_env("ADYEN_PAYMENTS_API_KEY"),
    kyc_api_key: System.get_env("ADYEN_LEM_API_KEY"),
    balance_api_key: System.get_env("ADYEN_BALANCE_API_KEY"),
    report_api_key: System.get_env("ADYEN_REPORT_API_KEY")
  ],
  live_prefix: System.get_env("ADYEN_LIVE_URL_PREFIX"),
  merchant_account: System.get_env("MERCHANT_ACCOUNT"),
  report_download_url: System.get_env("ADYEN_REPORT_DOWNLOAD_URL")

config :ex_firebase_auth, :issuer, System.get_env("EX_FIREBASE_ISSUER")

config :ex_firebase_auth_plug, environment: :local

config :ex_service_client,
  environment: :local,
  backend_endpoint: "api.sdlab",
  base_url: System.get_env("BACKEND_BASE_URL", "http://localhost:3000/api"),
  service_client_id: System.get_env("CLIENT_ID"),
  service_client_secret: System.get_env("CLIENT_SECRET"),
  service_name: System.get_env("SYSTEM")

config :goth, json: System.get_env("SERVICE_ACCOUNT")

# config :hammer,
#   backend:

# Delete after either svc is running in GKE or redis works with cloud run
#     {Hammer.Backend.Redis,

config :hammer,
  backend: {Hammer.Backend.ETS, [expiry_ms: 60_000 * 60 * 4, cleanup_interval_ms: 60_000 * 10]}

# ## SSL Support
#      [
#
#        # Do not include metadata nor timestamps in development logs
# In order to use HTTPS in development, a self-signed
#        # Mix task:
# certificate can be generated by running the following
#        #
#        #     mix phx.gen.cert
#        #
#        key_prefix: "orders_svc:draft_rate_limiter",
config :joken, default_signer: System.get_env("BACKEND_API_TOKEN")

# Do not include metadata nor timestamps in development logs
#        expiry_ms: 60_000 * 60 * 4,
# Mix task:
#        cleanup_interval_ms: 60_000 * 10,
#
#        redix_config: [
#     mix phx.gen.cert
#          host: System.get_env("REDIS_IP"),
#
#          port: "REDIS_PORT" |> System.get_env() |> String.to_integer()
#        ]
#      ]}
# https://github.com/stagedates/README/blob/main/coding/api_documentation.md#serve-the-spec-and-swaggerui
# Run `mix help phx.gen.cert` for more information.
# For development, we disable any cache and enable
#
config :logger, :console, format: "[$level] $message\n"

config :open_api_spex, :cache_adapter, OpenApiSpex.Plug.NoneCache

# debugging and code reloading.
# The `http:` config above can be replaced with:
#
#
config :orders_service, OrdersService.Repo,
  # The watchers configuration can be used to run external
  #     https: [
  after_connect: {Postgrex, :query!, ["SET search_path TO orders, public", []]},
  # watchers to your application. For example, we can use it
  #       port: 4001,
  migration_timestamps: [type: :naive_datetime_usec],
  # to bundle .js and .css sources.
  #       cipher_suite: :strong,
  #       keyfile: "priv/cert/selfsigned_key.pem",
  migration_source: "orders_schema_migrations",
  seed_source: "orders_seeds"

config :orders_service, OrdersServiceWeb.Endpoint,
  # Binding to loopback ipv4 address prevents access from other machines.
  #       certfile: "priv/cert/selfsigned.pem"

  # Change to `ip: {0, 0, 0, 0}` to allow access from other machines.
  #     ],
  #
  # If desired, both `http:` and `https:` keys can be
  # configured to run both http and https servers on
  # different ports.

  http: [
    compress: true,
    ip: {127, 0, 0, 1},
    port: String.to_integer(System.get_env("PHX_PORT", "4000"))
  ],
  check_origin: false,
  code_reloader: true,
  debug_errors: true,
  secret_key_base: "vL50UbU2kZ59KT7i0YzXB6Mgm1nVCtwnR2c/jBE0Z1dg92ubu51fcUunfhFp5NuK",
  watchers: [
    esbuild: {Esbuild, :install_and_run, [:default, ~w(--sourcemap=inline --watch)]}
  ]

config :orders_service,
  db_heartbeat_interval: 60

# Enable dev routes for dashboard and mailbox
config :orders_service, dev_routes: true

# Initialize plugs at runtime for faster development compilation
config :phoenix, :plug_init_mode, :runtime

# Set a higher stacktrace during development. Avoid configuring such
# in production as building large stacktraces may be expensive.
config :phoenix, :stacktrace_depth, 20

config :seatsio,
  admin_key: System.get_env("SEATSIO_ADMIN_KEY"),
  public_default_workspace_key: System.get_env("SEATSIO_PUBLIC_DEFAULT_WORKSPACE_KEY"),
  private_default_workspace_key: System.get_env("SEATSIO_PRIVATE_DEFAULT_WORKSPACE_KEY")
